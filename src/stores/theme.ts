import { ref, watchEffect } from 'vue';

const THEME_PREFERENCE_STORAGE_KEY = 'app-theme-preference';

// Reactive state for the user's theme preference
const themePreference = ref(localStorage.getItem(THEME_PREFERENCE_STORAGE_KEY) || 'system');

// Reactive state for the current theme
const isDark = ref(false);

// Function to apply the theme to the document element
const applyTheme = (dark: boolean) => {
  if (dark) {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }
};

// Watch for changes in isDark and apply the theme
watchEffect(() => {
  applyTheme(isDark.value);
});

// Watch for changes in themePreference and update isDark
watchEffect(() => {
  if (themePreference.value === 'system') {
    isDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches;
  } else {
    isDark.value = themePreference.value === 'dark';
  }
  localStorage.setItem(THEME_PREFERENCE_STORAGE_KEY, themePreference.value);
});

// Function to toggle the theme preference
export const toggleTheme = () => {
  const preferences = ['system', 'light', 'dark'];
  const currentIndex = preferences.indexOf(themePreference.value);
  themePreference.value = preferences[(currentIndex + 1) % preferences.length];
};

// Expose the theme state and preference
export const theme = {
  isDark,
  themePreference
};