import { ref, watch } from 'vue'

export interface LLMRole {
  id: string;
  name: string;
  description: string;
  prompt: string;
}

const LOCAL_STORAGE_KEY = 'custom-llm-roles';

// Load roles from localStorage
const loadRoles = (): LLMRole[] => {
  try {
    const savedRoles = localStorage.getItem(LOCAL_STORAGE_KEY);
    return savedRoles ? JSON.parse(savedRoles) : [];
  } catch (e) {
    console.error("Failed to load custom LLM roles from localStorage:", e);
    return [];
  }
};

// Reactive state for custom roles
export const customLLMRoles = ref<LLMRole[]>(loadRoles());

// Watch for changes and save to localStorage
watch(customLLMRoles, (newRoles) => {
  try {
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(newRoles));
  } catch (e) {
    console.error("Failed to save custom LLM roles to localStorage:", e);
  }
}, { deep: true });

// Function to add a new role
export const addLLMRole = (role: LLMRole) => {
  customLLMRoles.value.push(role);
};

// Function to update an existing role
export const updateLLMRole = (updatedRole: LLMRole) => {
  const index = customLLMRoles.value.findIndex(r => r.id === updatedRole.id);
  if (index !== -1) {
    customLLMRoles.value[index] = updatedRole;
  }
};

// Function to delete a role
export const deleteLLMRole = (roleId: string) => {
  customLLMRoles.value = customLLMRoles.value.filter(r => r.id !== roleId);
};

// Initialize with some default roles if none exist
if (customLLMRoles.value.length === 0) {
  addLLMRole({
    id: 'critical-thinker',
    name: '批判性思考者',
    description: '擅长发现问题、质疑假设、分析逻辑漏洞。',
    prompt: '你是一个批判性思考者，请对当前讨论提出质疑和挑战。',
  });
  addLLMRole({
    id: 'creative-innovator',
    name: '创意创新者',
    description: '擅长提出新颖的想法、跳出框架思考、寻找替代方案。',
    prompt: '你是一个创意创新者，请提出一些新颖的、非传统的想法。',
  });
  addLLMRole({
    id: 'system-analyst',
    name: '系统分析师',
    description: '擅长从整体角度分析问题，识别相互关联的要素和潜在的系统性影响。',
    prompt: '你是一个系统分析师，请从系统性角度分析当前讨论，识别相互关系和潜在影响。',
  });
  addLLMRole({
    id: 'ethical-advisor',
    name: '伦理顾问',
    description: '擅长从道德和伦理角度审视问题，评估潜在的社会影响。',
    prompt: '你是一个伦理顾问，请从道德和伦理角度对当前讨论发表看法。',
  });
}
