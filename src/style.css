@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-slate-200 dark:border-slate-700;
  }

  body {
    @apply bg-slate-50 dark:bg-slate-900 text-slate-900 dark:text-slate-100;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color 0.2s ease, color 0.2s ease;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold text-slate-900 dark:text-slate-100;
    line-height: 1.3;
    letter-spacing: -0.025em;
  }

  p {
    line-height: 1.6;
    @apply text-slate-700 dark:text-slate-300;
  }
}

@layer components {
  .btn-primary {
    @apply bg-slate-900 hover:bg-slate-800 dark:bg-slate-100 dark:hover:bg-slate-200 text-white dark:text-slate-900 font-medium py-3 px-6 rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply bg-white dark:bg-slate-800 hover:bg-slate-50 dark:hover:bg-slate-700 text-slate-700 dark:text-slate-300 font-medium py-3 px-6 rounded-lg border border-slate-200 dark:border-slate-600 transition-colors duration-200 shadow-sm hover:shadow-md;
  }

  .card {
    @apply bg-white dark:bg-slate-800 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 border border-slate-200 dark:border-slate-700;
  }

  .input-field {
    @apply w-full px-4 py-3 rounded-lg border border-slate-300 dark:border-slate-600 focus:border-slate-500 dark:focus:border-slate-400 focus:ring-2 focus:ring-slate-200 dark:focus:ring-slate-700 transition-colors duration-200 outline-none bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100 placeholder-slate-500 dark:placeholder-slate-400;
  }

  .question-card {
    @apply card p-6 mb-4 hover:shadow-lg transition-shadow duration-200;
  }

  .category-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
  }

    .form-label {
    @apply block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2;
  }

  .form-input {
    @apply w-full px-3 py-2 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors;
  }

  .form-textarea {
    @apply w-full px-3 py-2 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors;
  }

  .stat-card {
    @apply bg-slate-100 dark:bg-slate-800 p-4 rounded-lg text-center border border-slate-200 dark:border-slate-700;
  }

  .stat-value {
    @apply text-2xl font-bold text-slate-900 dark:text-slate-100;
  }

  .stat-label {
    @apply text-xs text-slate-600 dark:text-slate-400 uppercase tracking-wide;
  }

  .insight-card {
    @apply bg-slate-50 dark:bg-slate-800 p-4 rounded-lg text-sm text-slate-700 dark:text-slate-300 border border-slate-200 dark:border-slate-700;
  }

  .btn-secondary-sm {
    @apply bg-white dark:bg-slate-800 hover:bg-slate-50 dark:hover:bg-slate-700 text-slate-600 dark:text-slate-300 font-medium py-2 px-3 rounded-lg border border-slate-200 dark:border-slate-600 transition-colors duration-200 text-sm flex items-center;
  }

  .btn-icon {
    @apply p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-500 dark:text-slate-400 transition-colors duration-200;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-slate-900 to-slate-700 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent;
  }

  .bg-gradient-primary {
    @apply bg-gradient-to-r from-slate-900 to-slate-700 dark:from-slate-100 dark:to-slate-300;
  }

  .glass-effect {
    @apply backdrop-blur-sm bg-white/80 dark:bg-slate-800/80;
  }
}

/* 简化的动画效果 */

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* 暗色模式滚动条 */
.dark ::-webkit-scrollbar-thumb {
  background: rgba(71, 85, 105, 0.3);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 85, 105, 0.5);
}

/* 选择文本样式 */
::selection {
  background: rgba(59, 130, 246, 0.1);
  color: rgb(59, 130, 246);
}

.dark ::selection {
  background: rgba(59, 130, 246, 0.2);
  color: rgb(147, 197, 253);
}

/* 焦点样式优化 */
button:focus-visible,
input:focus-visible {
  outline: 2px solid rgb(59, 130, 246);
  outline-offset: 2px;
}

.dark button:focus-visible,
.dark input:focus-visible {
  outline-color: rgb(147, 197, 253);
}

/* 加载动画 */
.loading-dots {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.loading-dots > div {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: currentColor;
  opacity: 0.4;
  animation: pulse 1.5s ease-in-out infinite;
}

.loading-dots > div:nth-child(1) { animation-delay: 0s; }
.loading-dots > div:nth-child(2) { animation-delay: 0.2s; }
.loading-dots > div:nth-child(3) { animation-delay: 0.4s; }

@keyframes pulse {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 1; }
}

