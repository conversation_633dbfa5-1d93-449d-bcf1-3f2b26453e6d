import { createRouter, createWebHistory } from 'vue-router'
import ThinkingTrainer from './components/ThinkingTrainer.vue'
import AnalysisPage from './components/AnalysisPage.vue'
import RoundtablePage from './components/RoundtablePage.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: ThinkingTrainer
  },
  {
    path: '/analysis',
    name: 'Analysis',
    component: AnalysisPage
  },
  {
    path: '/roundtable',
    name: 'Roundtable',
    component: RoundtablePage
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router