<script setup lang="ts">
import { computed } from 'vue'
import { language } from '../stores/language'
import { theme, toggleTheme } from '../stores/theme'

const getThemeIcon = () => {
  switch (theme.themePreference.value) {
    case 'light':
      return '☀️'
    case 'dark':
      return '🌙'
    case 'system':
      return '🌓'
    default:
      return '🌓'
  }
}

const getThemeLabel = computed(() => {
  if (language.value === 'zh') {
    switch (theme.themePreference.value) {
      case 'light':
        return '浅色模式'
      case 'dark':
        return '深色模式'
      case 'system':
        return '跟随系统'
      default:
        return '跟随系统'
    }
  } else {
    switch (theme.themePreference.value) {
      case 'light':
        return 'Light Mode'
      case 'dark':
        return 'Dark Mode'
      case 'system':
        return 'System'
      default:
        return 'System'
    }
  }
})
</script>

<template>
  <button
    @click="toggleTheme"
    class="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors duration-200 text-slate-600 dark:text-slate-300"
    :title="getThemeLabel"
  >
    <span class="text-lg">{{ getThemeIcon() }}</span>
    <span class="text-sm font-medium hidden sm:inline">{{ getThemeLabel }}</span>
  </button>
</template>