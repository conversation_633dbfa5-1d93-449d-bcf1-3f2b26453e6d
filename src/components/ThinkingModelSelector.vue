<script setup lang="ts">
import { computed, ref } from 'vue'
import { getAllThinkingModelDetails, defaultThinkingModels, deleteCustomModel, type ThinkingModel, type ThinkingModelDetail } from '../utils/systemicThinking'
import { language } from '../stores/language'

interface Props {
  modelValue: ThinkingModel[]
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: ThinkingModel[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Use a ref to make allModels reactive to changes (e.g., after deletion)
const availableModels = ref<ThinkingModelDetail[]>(getAllThinkingModelDetails())

const selectedModels = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const translations = computed(() => {
  return {
    selectModels: language.value === 'zh' ? '选择思维模型' : 'Select Thinking Models',
    modelDescription: language.value === 'zh' ? '选择一个或多个思维模型进行分析' : 'Select one or more thinking models for analysis',
    confirmDeleteModel: language.value === 'zh' ? '确定要删除此自定义思维模型吗？' : 'Are you sure you want to delete this custom thinking model?',
  }
})

const toggleModel = (modelId: ThinkingModel) => {
  const currentSelection = new Set(selectedModels.value)
  if (currentSelection.has(modelId)) {
    currentSelection.delete(modelId)
  } else {
    currentSelection.add(modelId)
  }
  selectedModels.value = Array.from(currentSelection)
}

const isModelSelected = (modelId: ThinkingModel) => {
  return selectedModels.value.includes(modelId)
}

const isCustomModel = (modelId: ThinkingModel) => {
  return !defaultThinkingModels.some(model => model.id === modelId)
}

const handleDeleteCustomModel = (modelId: ThinkingModel) => {
  if (confirm(translations.value.confirmDeleteModel)) {
    deleteCustomModel(modelId)
    // Re-fetch models after deletion
    availableModels.value = getAllThinkingModelDetails()
    // Also remove from selected if it was selected
    if (isModelSelected(modelId)) {
      selectedModels.value = selectedModels.value.filter(id => id !== modelId)
    }
  }
}
</script>

<template>
  <div class="space-y-4">
    <div>
      <label class="form-label">{{ translations.selectModels }}</label>
      <p class="form-help-text">{{ translations.modelDescription }}</p>
    </div>
    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
      <div
        v-for="model in availableModels"
        :key="model.id"
        :class="[
          'relative flex items-center p-3 rounded-lg border transition-all duration-200 ease-in-out',
          isModelSelected(model.id)
            ? 'bg-blue-500 border-blue-500 text-white shadow-md'
            : 'bg-white border-slate-300 text-slate-700 hover:border-blue-400 hover:shadow-sm dark:bg-slate-700 dark:border-slate-600 dark:text-slate-200 dark:hover:border-blue-400',
          disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
        ]"
      >
        <button
          @click.stop="toggleModel(model.id)"
          :disabled="disabled"
          class="flex items-center w-full h-full"
        >
          <span class="text-xl mr-3 flex-shrink-0">{{ model.icon }}</span>
          <div class="flex-grow text-left">
            <h4 :class="['font-semibold', isModelSelected(model.id) ? 'text-white' : 'text-slate-800 dark:text-slate-100']">{{ model.name }}</h4>
            <p :class="['text-sm', isModelSelected(model.id) ? 'text-blue-100' : 'text-slate-500 dark:text-slate-400']">{{ model.description }}</p>
          </div>
        </button>
        <button
          v-if="isCustomModel(model.id)"
          @click.stop="handleDeleteCustomModel(model.id)"
          :disabled="disabled"
          class="absolute top-1 right-1 p-1 rounded-full bg-red-500 text-white hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
          aria-label="Delete custom model"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
        </button>
      </div>
    </div>
  </div>
</template>