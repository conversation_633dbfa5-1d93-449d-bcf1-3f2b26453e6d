<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { customLLMRoles, addLLMRole, updateLLMRole, deleteLLMRole, LLMRole } from '../stores/llmRoles'

interface ModelConfig {
  id: string
  name: string
  provider: 'gemini' | 'deepseek' | 'zhipu' | 'custom'
  apiKey: string
  baseUrl?: string
  isActive: boolean
  isCustom?: boolean
}

const isOpen = ref(false)
const showAddCustom = ref(false)
const newModelName = ref('')
const newModelProvider = ref<'gemini' | 'deepseek' | 'zhipu' | 'custom'>('custom')
const newModelBaseUrl = ref('')
const newModelApiKey = ref('')

const showLLMRoleManager = ref(false)
const newLLMRoleName = ref('')
const newLLMRoleDescription = ref('')
const newLLMRolePrompt = ref('')
const editingLLMRole = ref<LLMRole | null>(null)

// 拖拽相关状态
const isDragging = ref(false)
const dragOffset = ref({ x: 0, y: 0 })
const modalPosition = ref({ x: 0, y: 0 })

const models = ref<ModelConfig[]>([
  {
    id: 'gemini-2.5-flash',
    name: 'Gemini 2.5 Flash',
    provider: 'gemini',
    apiKey: '',
    baseUrl: 'https://generativelanguage.googleapis.com',
    isActive: true
  },
  {
    id: 'deepseek-chat',
    name: 'DeepSeek V3',
    provider: 'deepseek',
    apiKey: '',
    baseUrl: 'https://api.deepseek.com',
    isActive: false
  },
  {
    id: 'glm-4-flashx',
    name: 'GLM 4 Flash X',
    provider: 'zhipu',
    apiKey: '',
    baseUrl: 'https://open.bigmodel.cn/api/paas/v4',
    isActive: false
  }
])

const activeModel = computed(() => models.value.find(m => m.isActive))

const toggleSettings = () => {
  isOpen.value = !isOpen.value
}

const setActiveModel = (modelId: string) => {
  models.value.forEach(model => {
    model.isActive = model.id === modelId
  })
  saveSettings()
}

// 拖拽功能
const startDrag = (event: MouseEvent) => {
  isDragging.value = true
  const rect = (event.target as HTMLElement).closest('.draggable-modal')?.getBoundingClientRect()
  if (rect) {
    dragOffset.value = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    }
  }
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
}

const onDrag = (event: MouseEvent) => {
  if (!isDragging.value) return
  
  const newX = event.clientX - dragOffset.value.x
  const newY = event.clientY - dragOffset.value.y
  
  // 限制在视窗内
  const maxX = window.innerWidth - 400 // 模态框宽度
  const maxY = window.innerHeight - 300 // 模态框高度
  
  modalPosition.value = {
    x: Math.max(0, Math.min(newX, maxX)),
    y: Math.max(0, Math.min(newY, maxY))
  }
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
}

const addCustomModel = () => {
  if (!newModelName.value.trim()) return
  
  const customModel: ModelConfig = {
    id: `custom-${Date.now()}`,
    name: newModelName.value.trim(),
    provider: newModelProvider.value,
    apiKey: newModelApiKey.value.trim(),
    baseUrl: newModelBaseUrl.value.trim() || 'https://api.example.com',
    isActive: false,
    isCustom: true
  }
  
  models.value.push(customModel)
  saveSettings()
  
  // 重置表单和位置
  newModelName.value = ''
  newModelProvider.value = 'custom'
  newModelBaseUrl.value = ''
  newModelApiKey.value = ''
  showAddCustom.value = false
  modalPosition.value = { x: 0, y: 0 }
}

const removeCustomModel = (modelId: string) => {
  const modelIndex = models.value.findIndex(m => m.id === modelId)
  if (modelIndex > -1 && models.value[modelIndex].isCustom) {
    if (models.value[modelIndex].isActive && models.value.length > 1) {
      models.value[0].isActive = true
    }
    models.value.splice(modelIndex, 1)
    saveSettings()
  }
}

const openAddLLMRoleModal = () => {
  editingLLMRole.value = null
  newLLMRoleName.value = ''
  newLLMRoleDescription.value = ''
  newLLMRolePrompt.value = ''
  showLLMRoleManager.value = true
}

const openEditLLMRoleModal = (role: LLMRole) => {
  editingLLMRole.value = { ...role }
  newLLMRoleName.value = role.name
  newLLMRoleDescription.value = role.description
  newLLMRolePrompt.value = role.prompt
  showLLMRoleManager.value = true
}

const saveLLMRole = () => {
  if (!newLLMRoleName.value.trim() || !newLLMRolePrompt.value.trim()) {
    alert('角色名称和提示词不能为空！')
    return
  }

  const roleData: LLMRole = {
    id: editingLLMRole.value?.id || `llm-role-${Date.now()}`,
    name: newLLMRoleName.value.trim(),
    description: newLLMRoleDescription.value.trim(),
    prompt: newLLMRolePrompt.value.trim(),
  }

  if (editingLLMRole.value) {
    updateLLMRole(roleData)
  } else {
    addLLMRole(roleData)
  }
  cancelLLMRoleEdit()
}

const cancelLLMRoleEdit = () => {
  showLLMRoleManager.value = false
  editingLLMRole.value = null
  newLLMRoleName.value = ''
  newLLMRoleDescription.value = ''
  newLLMRolePrompt.value = ''
}

const deleteLLMRoleConfirmed = (roleId: string) => {
  if (confirm('确定要删除此LLM角色吗？')) {
    deleteLLMRole(roleId)
  }
}

const saveSettings = () => {
  localStorage.setItem('model-settings', JSON.stringify(models.value))
}

const loadSettings = () => {
  const saved = localStorage.getItem('model-settings')
  if (saved) {
    try {
      const savedModels = JSON.parse(saved)
      savedModels.forEach((savedModel: ModelConfig) => {
        const existingIndex = models.value.findIndex(m => m.id === savedModel.id)
        if (existingIndex > -1) {
          models.value[existingIndex] = { ...models.value[existingIndex], ...savedModel }
        } else if (savedModel.isCustom) {
          models.value.push(savedModel)
        }
      })
    } catch (error) {
      console.error('Failed to load model settings:', error)
    }
  }
}

const getProviderColor = (provider: string) => {
  switch (provider) {
    case 'gemini': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
    case 'deepseek': return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
    case 'zhipu': return 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
    case 'custom': return 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300'
    default: return 'bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300'
  }
}

onMounted(() => {
  loadSettings()
  // 初始化模态框位置为屏幕中央
  modalPosition.value = {
    x: (window.innerWidth - 400) / 2,
    y: (window.innerHeight - 300) / 2
  }
})
</script>

<template>
  <div class="relative">
    <!-- 模型状态指示器 -->
    <button
      @click="toggleSettings"
      class="flex items-center space-x-1.5 sm:space-x-2 px-2 sm:px-4 py-1.5 sm:py-2 rounded-lg glass-effect hover:bg-slate-100/80 dark:hover:bg-slate-800/80 transition-all duration-200 text-slate-600 dark:text-slate-300 hover:text-slate-800 dark:hover:text-slate-100 transform hover:scale-105"
      title="模型设置"
    >
      <div class="flex items-center space-x-1 sm:space-x-2">
        <div :class="['w-2 h-2 rounded-full', activeModel?.apiKey ? 'bg-green-500' : 'bg-orange-500']"></div>
        <span class="text-xs sm:text-sm font-medium truncate max-w-20 sm:max-w-none">{{ activeModel?.name || '未配置' }}</span>
      </div>
      <svg class="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
      </svg>
    </button>
    
    <Teleport to="body">
      <!-- 设置面板 -->
      <div
        v-if="isOpen"
        class="fixed bottom-20 sm:bottom-24 right-2 sm:right-4 w-[95vw] sm:w-[480px] max-w-[480px] rounded-2xl shadow-2xl glass-morphism border-t border-white/20 dark:border-slate-700/50 p-4 sm:p-6 z-[100] animate-slide-up"
        style="max-height: calc(100vh - 200px); overflow-y: auto;"
      >
        <div class="flex items-center justify-between mb-4 sm:mb-6">
          <h3 class="text-base sm:text-lg font-semibold text-slate-800 dark:text-slate-200">模型设置</h3>
          <div class="flex items-center space-x-2">
            <button
              @click="showAddCustom = true"
              class="text-xs sm:text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium transition-colors px-2 sm:px-3 py-1 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20"
            >
              + 添加模型
            </button>
            <button
              @click="openAddLLMRoleModal"
              class="text-xs sm:text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium transition-colors px-2 sm:px-3 py-1 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20"
            >
              管理LLM角色
            </button>
            <button
              @click="toggleSettings"
              class="p-1 rounded-lg hover:bg-slate-100/50 dark:hover:bg-slate-700/50 text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
            >
              <svg class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div class="space-y-3 sm:space-y-4 max-h-80 sm:max-h-96 overflow-y-auto">
          <div v-for="model in models" :key="model.id" class="bg-slate-50 dark:bg-slate-700 rounded-lg p-3 sm:p-4 border border-slate-200 dark:border-slate-600">
            <div class="flex items-center justify-between mb-2 sm:mb-3">
              <div class="flex items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
                <input
                  :id="model.id"
                  type="radio"
                  :checked="model.isActive"
                  @change="setActiveModel(model.id)"
                  class="w-3 h-3 sm:w-4 sm:h-4 text-blue-600 border-slate-300 dark:border-slate-600 focus:ring-blue-500 dark:focus:ring-blue-400 flex-shrink-0"
                />
                <label :for="model.id" class="font-medium text-sm sm:text-base text-slate-800 dark:text-slate-200 truncate">
                  {{ model.name }}
                </label>
              </div>
              <div class="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
                <span :class="['px-1.5 sm:px-2 py-0.5 sm:py-1 text-xs rounded-full', getProviderColor(model.provider)]">
                  {{ model.provider.toUpperCase() }}
                </span>
                <button
                  v-if="model.isCustom"
                  @click="removeCustomModel(model.id)"
                  class="p-1 text-slate-400 dark:text-slate-500 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                  title="删除自定义模型"
                >
                  <svg class="h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
            
            <!-- Base URL -->
            <div class="mb-2 sm:mb-3">
              <label :for="`${model.id}-url`" class="block text-xs sm:text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                Base URL
              </label>
              <input
                :id="`${model.id}-url`"
                v-model="model.baseUrl"
                type="url"
                :placeholder="`${model.name} Base URL`"
                class="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-xs sm:text-sm rounded-lg border border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-800 transition-all duration-200 outline-none bg-white dark:bg-slate-700"
                @input="saveSettings"
              />
            </div>
            
            <!-- API Key -->
            <div>
              <label :for="`${model.id}-key`" class="block text-xs sm:text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                API Key
              </label>
              <input
                :id="`${model.id}-key`"
                v-model="model.apiKey"
                type="password"
                :placeholder="`输入 ${model.name} API Key`"
                class="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-xs sm:text-sm rounded-lg border border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-800 transition-all duration-200 outline-none bg-white dark:bg-slate-700"
                @input="saveSettings"
              />
            </div>
          </div>
        </div>

        <div class="mt-4 sm:mt-6 p-3 sm:p-4 bg-blue-50/80 dark:bg-blue-900/20 border border-blue-200/50 dark:border-blue-800/50 rounded-lg glass-effect">
          <div class="flex items-start">
            <svg class="h-4 w-4 sm:h-5 sm:w-5 text-blue-500 dark:text-blue-400 mt-0.5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div class="text-xs sm:text-sm text-blue-700 dark:text-blue-300">
              <p class="font-medium mb-1">获取API Key：</p>
              <ul class="text-xs space-y-1">
                <li>• Gemini: <a href="https://makersuite.google.com/app/apikey" target="_blank" class="underline hover:text-blue-800 dark:hover:text-blue-200">Google AI Studio</a></li>
                <li>• DeepSeek: <a href="https://platform.deepseek.com/api-keys" target="_blank" class="underline hover:text-blue-800 dark:hover:text-blue-200">DeepSeek Platform</a></li>
                <li>• Zhipu: <a href="https://open.bigmodel.cn/" target="_blank" class="underline hover:text-blue-800 dark:hover:text-blue-200">Zhipu AI</a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 可拖拽的自定义模型弹窗 -->
      <div v-if="showAddCustom" class="fixed inset-0 bg-black/50 z-[101] flex items-center justify-center p-2 sm:p-4" @click.self="showAddCustom = false">
        <div
          class="relative w-full max-w-md rounded-2xl shadow-2xl glass-morphism draggable-modal border-t border-white/20 dark:border-slate-700/50"
          :style="{
            position: 'fixed',
            left: modalPosition.x + 'px',
            top: modalPosition.y + 'px',
          }"
        >
          <!-- 可拖拽的标题栏 -->
          <div
            class="flex items-center justify-between p-3 sm:p-4 border-b border-white/10 dark:border-slate-700/30 cursor-grab active:cursor-grabbing"
            @mousedown="startDrag"
          >
            <h3 class="text-base sm:text-lg font-semibold text-slate-800 dark:text-slate-200">添加自定义模型</h3>
            <div class="flex items-center space-x-2">
              <div class="text-xs text-slate-500 dark:text-slate-400 animate-pulse hidden sm:block">可拖拽</div>
              <button
                @click="showAddCustom = false"
                class="p-1 rounded-full glass-effect hover:bg-slate-100/80 dark:hover:bg-slate-700/80 text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 transition-all duration-200"
              >
                <svg class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          <div class="p-4 sm:p-6">
            <div class="space-y-3 sm:space-y-4">
              <div>
                <label class="block text-xs sm:text-sm font-medium text-slate-600 dark:text-slate-400 mb-2">模型名称</label>
                <input
                  v-model="newModelName"
                  type="text"
                  placeholder="例如：GPT-4 Turbo"
                  class="input-field text-sm"
                />
              </div>

              <div>
                <label class="block text-xs sm:text-sm font-medium text-slate-600 dark:text-slate-400 mb-2">提供商类型</label>
                <select
                  v-model="newModelProvider"
                  class="input-field text-sm"
                >
                  <option value="custom">自定义</option>
                  <option value="deepseek">DeepSeek 兼容</option>
                  <option value="gemini">Gemini 兼容</option>
                  <option value="zhipu">Zhipu 兼容</option>
                </select>
              </div>

              <div>
                <label class="block text-xs sm:text-sm font-medium text-slate-600 dark:text-slate-400 mb-2">Base URL</label>
                <input
                  v-model="newModelBaseUrl"
                  type="url"
                  placeholder="https://api.example.com"
                  class="input-field text-sm"
                />
              </div>

              <div>
                <label class="block text-xs sm:text-sm font-medium text-slate-600 dark:text-slate-400 mb-2">API Key</label>
                <input
                  v-model="newModelApiKey"
                  type="password"
                  placeholder="输入API Key"
                  class="input-field text-sm"
                />
              </div>
            </div>

            <div class="flex justify-end space-x-3 sm:space-x-4 mt-6 sm:mt-8">
              <button
                @click="showAddCustom = false"
                class="px-4 sm:px-6 py-2 sm:py-3 rounded-lg transition-all duration-200 transform hover:scale-105 text-slate-600 dark:text-slate-300 hover:text-slate-800 dark:hover:text-slate-100 hover:bg-slate-100/50 dark:hover:bg-slate-800/50 text-xs sm:text-sm font-medium glass-effect"
              >
                取消
              </button>
              <button
                @click="addCustomModel"
                :disabled="!newModelName.trim()"
                class="btn-primary text-xs sm:text-sm py-2 px-3 sm:px-4 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                添加模型
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- LLM 角色管理弹窗 -->
      <div v-if="showLLMRoleManager" class="fixed inset-0 bg-black/50 z-[101] flex items-center justify-center p-2 sm:p-4" @click.self="cancelLLMRoleEdit">
        <div
          class="relative w-full max-w-xl rounded-2xl shadow-2xl glass-morphism draggable-modal border-t border-white/20 dark:border-slate-700/50"
          :style="{
            position: 'fixed',
            left: modalPosition.x + 'px',
            top: modalPosition.y + 'px',
          }"
        >
          <div
            class="flex items-center justify-between p-3 sm:p-4 border-b border-white/10 dark:border-slate-700/30 cursor-grab active:cursor-grabbing"
            @mousedown="startDrag"
          >
            <h3 class="text-base sm:text-lg font-semibold text-slate-800 dark:text-slate-200">{{ editingLLMRole ? '编辑' : '添加' }} LLM 角色</h3>
            <div class="flex items-center space-x-2">
              <div class="text-xs text-slate-500 dark:text-slate-400 animate-pulse hidden sm:block">可拖拽</div>
              <button
                @click="cancelLLMRoleEdit"
                class="p-1 rounded-full glass-effect hover:bg-slate-100/80 dark:hover:bg-slate-700/80 text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 transition-all duration-200"
              >
                <svg class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          <div class="p-4 sm:p-6">
            <div class="space-y-3 sm:space-y-4">
              <div>
                <label class="block text-xs sm:text-sm font-medium text-slate-600 dark:text-slate-400 mb-2">角色名称</label>
                <input
                  v-model="newLLMRoleName"
                  type="text"
                  placeholder="例如：批判性思考者"
                  class="input-field text-sm"
                />
              </div>

              <div>
                <label class="block text-xs sm:text-sm font-medium text-slate-600 dark:text-slate-400 mb-2">角色描述 (可选)</label>
                <textarea
                  v-model="newLLMRoleDescription"
                  placeholder="例如：擅长发现问题、质疑假设、分析逻辑漏洞。"
                  rows="2"
                  class="input-field text-sm"
                ></textarea>
              </div>

              <div>
                <label class="block text-xs sm:text-sm font-medium text-slate-600 dark:text-slate-400 mb-2">提示词 (Prompt)</label>
                <textarea
                  v-model="newLLMRolePrompt"
                  placeholder="例如：你是一个批判性思考者，请对当前讨论提出质疑和挑战。"
                  rows="4"
                  class="input-field text-sm"
                ></textarea>
              </div>
            </div>

            <div class="flex justify-end space-x-3 sm:space-x-4 mt-6 sm:mt-8">
              <button
                @click="cancelLLMRoleEdit"
                class="px-4 sm:px-6 py-2 sm:py-3 rounded-lg transition-all duration-200 transform hover:scale-105 text-slate-600 dark:text-slate-300 hover:text-slate-800 dark:hover:text-slate-100 hover:bg-slate-100/50 dark:hover:bg-slate-800/50 text-xs sm:text-sm font-medium glass-effect"
              >
                取消
              </button>
              <button
                @click="saveLLMRole"
                :disabled="!newLLMRoleName.trim() || !newLLMRolePrompt.trim()"
                class="btn-primary text-xs sm:text-sm py-2 px-3 sm:px-4 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {{ editingLLMRole ? '保存' : '添加' }}角色
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- LLM 角色列表弹窗 -->
      <div v-if="showLLMRoleManager && !editingLLMRole" class="fixed inset-0 bg-black/50 z-[101] flex items-center justify-center p-2 sm:p-4" @click.self="cancelLLMRoleEdit">
        <div
          class="relative w-full max-w-xl rounded-2xl shadow-2xl glass-morphism draggable-modal border-t border-white/20 dark:border-slate-700/50"
          :style="{
            position: 'fixed',
            left: modalPosition.x + 'px',
            top: modalPosition.y + 'px',
          }"
        >
          <div
            class="flex items-center justify-between p-3 sm:p-4 border-b border-white/10 dark:border-slate-700/30 cursor-grab active:cursor-grabbing"
            @mousedown="startDrag"
          >
            <h3 class="text-base sm:text-lg font-semibold text-slate-800 dark:text-slate-200">LLM 角色管理</h3>
            <div class="flex items-center space-x-2">
              <button
                @click="openAddLLMRoleModal"
                class="text-xs sm:text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium transition-colors px-2 sm:px-3 py-1 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20"
              >
                + 添加新角色
              </button>
              <div class="text-xs text-slate-500 dark:text-slate-400 animate-pulse hidden sm:block">可拖拽</div>
              <button
                @click="cancelLLMRoleEdit"
                class="p-1 rounded-full glass-effect hover:bg-slate-100/80 dark:hover:bg-slate-700/80 text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 transition-all duration-200"
              >
                <svg class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          <div class="p-4 sm:p-6 max-h-96 overflow-y-auto">
            <div v-if="customLLMRoles.length === 0" class="text-center text-slate-500 dark:text-slate-400 py-8">
              暂无自定义LLM角色。点击“添加新角色”创建。
            </div>
            <div v-else class="space-y-3 sm:space-y-4">
              <div v-for="role in customLLMRoles" :key="role.id" class="bg-slate-50 dark:bg-slate-700 rounded-lg p-3 sm:p-4 border border-slate-200 dark:border-slate-600">
                <div class="flex items-center justify-between mb-2">
                  <h4 class="font-medium text-sm sm:text-base text-slate-800 dark:text-slate-200">{{ role.name }}</h4>
                  <div class="flex space-x-2">
                    <button
                      @click="openEditLLMRoleModal(role)"
                      class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-xs sm:text-sm"
                    >
                      编辑
                    </button>
                    <button
                      @click="deleteLLMRoleConfirmed(role.id)"
                      class="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 text-xs sm:text-sm"
                    >
                      删除
                    </button>
                  </div>
                </div>
                <p v-if="role.description" class="text-xs sm:text-sm text-slate-600 dark:text-slate-400 mb-2">{{ role.description }}</p>
                <p class="text-xs sm:text-sm text-slate-500 dark:text-slate-500 font-mono bg-slate-100 dark:bg-slate-800 p-2 rounded-md break-words">{{ role.prompt }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 遮罩层 -->
      <div
        v-if="isOpen"
        @click="toggleSettings"
        class="fixed inset-0 bg-black/20 backdrop-blur-sm z-[90]"
      ></div>
    </Teleport>
  </div>
</template>

<style scoped>
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.2s ease-out;
}

/* 自定义滚动条 */
.space-y-4::-webkit-scrollbar,
.space-y-3::-webkit-scrollbar {
  width: 4px;
}

.space-y-4::-webkit-scrollbar-track,
.space-y-3::-webkit-scrollbar-track {
  background: transparent;
}

.space-y-4::-webkit-scrollbar-thumb,
.space-y-3::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 2px;
}

.space-y-4::-webkit-scrollbar-thumb:hover,
.space-y-3::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

.dark .space-y-4::-webkit-scrollbar-thumb,
.dark .space-y-3::-webkit-scrollbar-thumb {
  background: rgba(71, 85, 105, 0.3);
}

.dark .space-y-4::-webkit-scrollbar-thumb:hover,
.dark .space-y-3::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 85, 105, 0.5);
}

/* 拖拽时禁用文本选择 */
.draggable-modal {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.glass-effect {
  background-color: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background-color: rgba(30, 41, 59, 0.4);
  border: 1px solid rgba(71, 85, 105, 0.2);
}

.glass-morphism {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.dark .glass-morphism {
  background-color: rgba(30, 41, 59, 0.95);
  border: 1px solid rgba(71, 85, 105, 0.3);
}

.input-field {
  @apply w-full px-3 py-2 rounded-lg border border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-800 transition-all duration-200 outline-none bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 placeholder:text-slate-400 dark:placeholder:text-slate-500;
}

.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg;
}
</style>