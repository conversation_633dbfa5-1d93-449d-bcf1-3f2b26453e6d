<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { language } from '../stores/language'
import { getActiveModelConfig, callLLMWithHistory } from '../utils/geminiApi'
import { isLoading, startLoading, stopLoading, updateLoadingProgress } from '../utils/loadingState'
import { customLLMRoles } from '../stores/llmRoles'
import { renderMarkdown } from '../utils/markdownRenderer'

const topic = ref('')
const messages = ref<{ role: string; content: string }[]>([])
const userInput = ref('')
const activeModelConfig = ref(getActiveModelConfig())
const inheritedHints = ref<string[]>([]) // New ref for inherited hints
const currentLLMRoleIndex = ref(0) // Tracks which LLM role is currently speaking
const isLLMTurn = ref(true) // True if it's LL<PERSON>'s turn to speak, false if it's user's turn

const translations = computed(() => {
  return {
    pageTitle: language.value === 'zh' ? '圆桌会议' : 'Roundtable Meeting',
    topicLabel: language.value === 'zh' ? '讨论主题：' : 'Discussion Topic:',
    startMeeting: language.value === 'zh' ? '开始圆桌会议' : 'Start Roundtable Meeting',
    sendMessage: language.value === 'zh' ? '发送' : 'Send',
    inputPlaceholder: language.value === 'zh' ? '输入你的发言...' : 'Enter your message...',
    meetingStarted: language.value === 'zh' ? '圆桌会议已开始。' : 'Roundtable meeting has started.',
    initialPrompt: language.value === 'zh' ? '请设定会议议程或提出第一个问题。' : 'Please set the meeting agenda or ask the first question.',
    closeWindow: language.value === 'zh' ? '关闭窗口' : 'Close Window',
    noApiKey: language.value === 'zh' ? '请在设置中配置您的LLM API Key以启用圆桌会议功能。' : 'Please configure your LLM API Key in settings to enable roundtable meeting.',
  }
})

onMounted(() => {
  const urlParams = new URLSearchParams(window.location.search)
  const topicParam = urlParams.get('topic')
  if (topicParam) {
    topic.value = decodeURIComponent(topicParam)
    messages.value.push({ role: 'system', content: translations.value.meetingStarted })
    messages.value.push({ role: 'system', content: translations.value.initialPrompt })
  }
})

const triggerLLMRoleResponses = async () => {
  if (!activeModelConfig.value) {
    alert(translations.value.noApiKey)
    return
  }

  if (currentLLMRoleIndex.value >= customLLMRoles.value.length) {
    // All LLM roles have spoken, it's user's turn
    isLLMTurn.value = false
    currentLLMRoleIndex.value = 0 // Reset for next round
    return
  }

  isLLMTurn.value = true // Ensure LLM turn is active

  try {
    const role = customLLMRoles.value[currentLLMRoleIndex.value]
    let rolePrompt = `${role.prompt}\n\n当前讨论主题是: ${topic.value}`

    if (inheritedHints.value.length > 0) {
      rolePrompt += `\n\n以下是与主题相关的思考提示：\n${inheritedHints.value.map(hint => `- ${hint}`).join('\n')}`
    }

    rolePrompt += `\n\n以下是目前的讨论记录：\n${messages.value.map(msg => `${msg.role}: ${msg.content}`).join('\n')}\n\n请你作为${role.name}，发表你的看法。`

    // Add a placeholder message for the current role's response
    messages.value.push({ role: role.name, content: '' })
    const currentMessageIndex = messages.value.length - 1

    let fullResponseContent = ''
    for await (const chunk of callLLMWithHistory(
      messages.value.slice(0, currentMessageIndex), // Pass history up to the current message
      activeModelConfig.value,
      rolePrompt // Pass rolePrompt as systemMessage
    )) {
      fullResponseContent += chunk
      messages.value[currentMessageIndex].content = fullResponseContent
    }
    // 模拟思考时间
    await new Promise(resolve => setTimeout(resolve, 1000))

    currentLLMRoleIndex.value++
    // Recursively call for the next LLM role after a short delay
    setTimeout(triggerLLMRoleResponses, 500) 

  } catch (error) {
    console.error('LLM圆桌会议失败:', error)
    messages.value.push({ role: 'system', content: language.value === 'zh' ? 'LLM思考失败，请检查API配置或稍后再试。' : 'LLM failed to respond. Please check API configuration or try again later.' })
    isLLMTurn.value = false // Allow user to intervene on error
  }
}

const handleSendMessage = async () => {
  if (!userInput.value.trim()) return
  if (!activeModelConfig.value) {
    alert(translations.value.noApiKey)
    return
  }

  const userMessage = userInput.value.trim()
  messages.value.push({ role: 'user', content: userMessage })
  userInput.value = ''

  currentLLMRoleIndex.value = 0 // Reset for next round of LLM responses
  isLLMTurn.value = true // It's LLM's turn again
  await triggerLLMRoleResponses()
}

onMounted(() => {
  const urlParams = new URLSearchParams(window.location.search)
  const topicParam = urlParams.get('topic')
  const hintsParam = urlParams.get('hints')

  if (topicParam) {
    topic.value = decodeURIComponent(topicParam)
    messages.value.push({ role: 'system', content: translations.value.meetingStarted })
    messages.value.push({ role: 'system', content: translations.value.initialPrompt })

    if (hintsParam) {
      try {
        inheritedHints.value = JSON.parse(decodeURIComponent(hintsParam))
      } catch (e) {
        console.error('Failed to parse hints from URL:', e)
        inheritedHints.value = []
      }
    }
    currentLLMRoleIndex.value = 0 // Start from the first LLM role
    isLLMTurn.value = true // It's LLM's turn initially
    triggerLLMRoleResponses() // Auto-trigger LLM responses
  }
})


const closeWindow = () => {
  window.close()
}
</script>

<template>
  <div class="h-screen flex flex-col bg-slate-50 dark:bg-slate-900 text-slate-800 dark:text-slate-200">
    <!-- Header -->
    <header class="flex-shrink-0 bg-white/70 dark:bg-slate-800/70 backdrop-blur-lg border-b border-slate-200 dark:border-slate-700 z-20">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center space-x-4">
            <h1 class="text-xl font-bold truncate">{{ translations.pageTitle }}</h1>
            <p class="text-sm text-slate-500 dark:text-slate-400 hidden md:block truncate">{{ translations.topicLabel }} {{ topic }}</p>
          </div>
          <button @click="closeWindow" class="btn-icon">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
          </button>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 flex flex-col overflow-hidden p-4 sm:p-6">
      <div class="flex-1 overflow-y-auto space-y-4 p-4 bg-white dark:bg-slate-800 rounded-lg shadow-inner">
        <div v-for="(msg, index) in messages" :key="index" :class="{'text-right': msg.role === 'user', 'text-left': msg.role !== 'user'}">
            <div :class="[
              'inline-block p-3 rounded-lg max-w-[80%]',
              msg.role === 'user' ? 'bg-blue-500 text-white' : 'bg-slate-200 dark:bg-slate-700 text-slate-800 dark:text-slate-200'
            ]">
            <span v-if="msg.role !== 'user' && msg.role !== 'system'" class="font-semibold mr-2">{{ msg.role }}:</span>
            <span v-html="renderMarkdown(msg.content)"></span>
          </div>
        </div>
      </div>

      <!-- User Input -->
      <div class="mt-4 flex space-x-3">
        <input
          v-model="userInput"
          @keyup.enter="handleSendMessage"
          type="text"
          :placeholder="translations.inputPlaceholder"
          class="flex-1 form-input"
          :disabled="isLLMTurn"
        />
        <button @click="handleSendMessage" :disabled="!userInput.trim() || isLLMTurn" class="btn-primary">
          {{ translations.sendMessage }}
        </button>
      </div>
    </main>
  </div>
</template>

<style scoped>
.form-input {
  @apply block w-full px-4 py-2 border border-slate-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
  @apply bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 placeholder-slate-400 dark:placeholder-slate-500;
}

.btn-primary {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
  @apply dark:bg-blue-500 dark:hover:bg-blue-600 dark:focus:ring-blue-400;
}

.btn-icon {
  @apply p-2 rounded-full text-slate-400 hover:text-slate-500 hover:bg-slate-100 dark:text-slate-500 dark:hover:text-slate-400 dark:hover:bg-slate-700 transition-colors duration-200;
}
</style>