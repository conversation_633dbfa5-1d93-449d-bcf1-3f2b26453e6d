<script setup lang="ts">
import { ref, onMounted, watch, nextTick, shallowRef, computed, reactive } from 'vue'
import { type AnalysisNode, type NodeConnection, getAllThinkingModelDetails } from '../utils/systemicThinking'

interface Props {
  nodes: AnalysisNode[]
  connections: NodeConnection[]
  thinkingDepth: number
}

const props = defineProps<Props>()

const svgRef = ref<SVGElement>()
const selectedNode = shallowRef<AnalysisNode | null>(null)
const hoveredNode = shallowRef<AnalysisNode | null>(null)

const modelDetails = getAllThinkingModelDetails();

// 拖拽相关状态
const isDragging = ref(false)
const draggedNode = ref<AnalysisNode | null>(null)
const dragOffset = ref({ x: 0, y: 0 })

// 缩放和平移状态
const scale = ref(1)
const translate = ref({ x: 0, y: 0 })
const isPanning = ref(false)
const panStart = ref({ x: 0, y: 0 })

// 性能优化：使用 Map 缓存计算结果
const nodePositions = new Map<string, { x: number, y: number }>()
const connectionPaths = reactive(new Map<string, string>())

// 响应式视口尺寸
const viewportSize = ref({ width: 1200, height: 900 })

// 性能优化：计算属性缓存
const scalePercentage = computed(() => Math.round(scale.value * 100))

const nodeStrokeColor = computed(() => (node: AnalysisNode) => {
  const currentSelectedNode = selectedNode.value;
  if (currentSelectedNode && currentSelectedNode.id === node.id) {
    return '#1d4ed8';
  }
  return 'white';
});

const nodeStrokeWidth = computed(() => (node: AnalysisNode) => {
  const currentSelectedNode = selectedNode.value;
  if (currentSelectedNode && currentSelectedNode.id === node.id) {
    return 4;
  }
  return 3;
});

const nodeOpacity = computed(() => (node: AnalysisNode) => {
  if (!hoveredNode.value && !selectedNode.value) return 1;

  if (hoveredNode.value && hoveredNode.value.id !== node.id) {
    return 0.6; 
  }

  if (selectedNode.value && selectedNode.value.id !== node.id) {
    return 0.6;
  }

  return 1;
});

const getNodeColor = (node: AnalysisNode) => {
  const modelDetail = getAllThinkingModelDetails().find(m => m.id === node.modelSource);
  if (modelDetail && modelDetail.color) {
    return modelDetail.color;
  }
  switch (node.level) {
    case 0: return '#2563eb';
    case 1: return '#059669';
    case 2: return '#dc2626';
    case 3: return '#7c3aed';
    default: return '#ea580c';
  }
};

const getNodeSize = (level: number) => {
  const baseSize = 50
  const sizeReduction = Math.min(level * 8, 25)
  return Math.max(baseSize - sizeReduction, 20)
}

// 更新视口尺寸
const updateViewportSize = () => {
  if (svgRef.value) {
    const rect = svgRef.value.getBoundingClientRect()
    viewportSize.value = {
      width: Math.max(rect.width, 800),
      height: Math.max(rect.height, 600)
    }
  }
}

// 性能优化：使用 Web Workers 进行布局计算（模拟）
const layoutNodes = () => {
  if (!props.nodes.length) return

  const width = viewportSize.value.width
  const height = viewportSize.value.height
  const centerX = width / 2
  const centerY = height / 2

  // 清除缓存
  nodePositions.clear()
  connectionPaths.clear()

  // 按层级分组
  const nodesByLevel: { [key: number]: AnalysisNode[] } = {}
  props.nodes.forEach(node => {
    if (!nodesByLevel[node.level]) {
      nodesByLevel[node.level] = []
    }
    nodesByLevel[node.level].push(node)
  })

  // 为每个层级的节点分配位置
  Object.keys(nodesByLevel).forEach(levelStr => {
    const level = parseInt(levelStr)
    const nodesInLevel = nodesByLevel[level]
    const radius = level === 0 ? 0 : Math.min(120 + level * 140, Math.min(width, height) / 3)
    
    if (level === 0) {
      // 核心节点在中心
      const node = nodesInLevel[0]
      const pos = node.x !== undefined && node.y !== undefined ? { x: node.x, y: node.y } : { x: centerX, y: centerY }
      nodePositions.set(node.id, pos)
      node.x = pos.x
      node.y = pos.y
    } else {
      // 其他节点围绕中心分布
      const angleStep = (2 * Math.PI) / nodesInLevel.length
      nodesInLevel.forEach((node, index) => {
        const pos = node.x !== undefined && node.y !== undefined ? 
          { x: node.x, y: node.y } : 
          (() => {
            const angle = index * angleStep - Math.PI / 2
            return { x: centerX + radius * Math.cos(angle), y: centerY + radius * Math.sin(angle) }
          })()
        nodePositions.set(node.id, pos)
        node.x = pos.x
        node.y = pos.y
      })
    }
  })

  // 预计算连接路径
  props.connections.forEach(connection => {
    const path = getConnectionPath(connection)
    connectionPaths.set(`${connection.from}-${connection.to}`, path)
  })
}

// 鼠标事件处理 - 性能优化
const handleMouseDown = (event: MouseEvent, node?: AnalysisNode) => {
  event.preventDefault()
  
  if (node) {
    // 开始拖拽节点
    isDragging.value = true
    draggedNode.value = node
    const rect = svgRef.value?.getBoundingClientRect()
    if (rect) {
      dragOffset.value = {
        x: (event.clientX - rect.left) / scale.value - translate.value.x - node.x!,
        y: (event.clientY - rect.top) / scale.value - translate.value.y - node.y!
      }
    }
  } else {
    // 开始平移画布
    isPanning.value = true
    panStart.value = { x: event.clientX, y: event.clientY }
  }
}

// 性能优化：使用 requestAnimationFrame 进行平滑更新
let animationFrameId: number | null = null

const handleMouseMove = (event: MouseEvent) => {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
  }
  
  animationFrameId = requestAnimationFrame(() => {
    if (isDragging.value && draggedNode.value) {
      // 拖拽节点
      const rect = svgRef.value?.getBoundingClientRect()
      if (rect) {
        const newX = (event.clientX - rect.left) / scale.value - translate.value.x - dragOffset.value.x
        const newY = (event.clientY - rect.top) / scale.value - translate.value.y - dragOffset.value.y
        
        draggedNode.value.x = newX
        draggedNode.value.y = newY
        nodePositions.set(draggedNode.value.id, { x: newX, y: newY })
        
        // 重新计算相关连接
        props.connections.forEach(connection => {
          if (connection.from === draggedNode.value!.id || connection.to === draggedNode.value!.id) {
            const fromPos = nodePositions.get(connection.from)
            const toPos = nodePositions.get(connection.to)
            if (fromPos && toPos) {
              const path = calculateConnectionPath(fromPos, toPos)
              connectionPaths.set(`${connection.from}-${connection.to}`, path)
            }
          }
        })
      }
    } else if (isPanning.value) {
      // 平移画布
      const deltaX = event.clientX - panStart.value.x
      const deltaY = event.clientY - panStart.value.y
      
      translate.value = {
        x: translate.value.x + deltaX / scale.value,
        y: translate.value.y + deltaY / scale.value
      }
      
      panStart.value = { x: event.clientX, y: event.clientY }
    }
  })
}

const handleMouseUp = () => {
  isDragging.value = false
  draggedNode.value = null
  isPanning.value = false
  
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = null
  }
}

// 缩放处理 - 性能优化
const handleWheel = (event: WheelEvent) => {
  event.preventDefault()
  
  const rect = svgRef.value?.getBoundingClientRect()
  if (!rect) return
  
  const mouseX = event.clientX - rect.left
  const mouseY = event.clientY - rect.top
  
  const delta = event.deltaY > 0 ? 0.9 : 1.1
  const newScale = Math.max(0.1, Math.min(3, scale.value * delta))
  
  // 以鼠标位置为中心缩放
  const scaleRatio = newScale / scale.value
  translate.value = {
    x: mouseX - (mouseX - translate.value.x) * scaleRatio,
    y: mouseY - (mouseY - translate.value.y) * scaleRatio
  }
  
  scale.value = newScale
}

const selectNode = (node: AnalysisNode) => {
  selectedNode.value = selectedNode.value?.id === node.id ? null : node
}

const hoverNode = (node: AnalysisNode | null) => {
  hoveredNode.value = node
}

const calculateConnectionPath = (fromPos: { x: number, y: number }, toPos: { x: number, y: number }) => {
  const midX = (fromPos.x + toPos.x) / 2
  const midY = (fromPos.y + toPos.y) / 2
  const controlOffset = 50
  return `M ${fromPos.x} ${fromPos.y} Q ${midX} ${midY - controlOffset} ${toPos.x} ${toPos.y}`
}

const getConnectionPath = (connection: NodeConnection) => {
  const cacheKey = `${connection.from}-${connection.to}`
  if (connectionPaths.has(cacheKey)) {
    return connectionPaths.get(cacheKey)!
  }

  const fromPos = nodePositions.get(connection.from)
  const toPos = nodePositions.get(connection.to)
  
  if (!fromPos || !toPos) return ''
  
  const path = calculateConnectionPath(fromPos, toPos)
  connectionPaths.set(cacheKey, path)
  return path
}

const getConnectionOpacity = (connection: NodeConnection) => {
  if (!hoveredNode.value && !selectedNode.value) return 0.6
  
  const targetNode = hoveredNode.value || selectedNode.value
  if (connection.from === targetNode?.id || connection.to === targetNode?.id) {
    return 1
  }
  
  return 0.2
}

const getConnectionWidth = (connection: NodeConnection) => {
  const baseWidth = connection.type === 'strong' ? 3 : 2
  const strengthMultiplier = connection.strength || 1
  return baseWidth * strengthMultiplier
}

const selectedNodeModelName = computed(() => {
  if (selectedNode.value) {
    const modelDetail = getAllThinkingModelDetails().find(m => m.id === selectedNode.value!.modelSource);
    return modelDetail?.name || selectedNode.value!.modelSource;
  }
  return '';
});

const isApplyingRepulsion = ref(false)

// 重置视图
const resetView = () => {
  scale.value = 1
  translate.value = { x: 0, y: 0 }
}

const applyRepulsion = async () => {
  isApplyingRepulsion.value = true
  await nextTick()

  const nodes = props.nodes
  if (nodes.length < 2) {
    isApplyingRepulsion.value = false
    return
  }

  const iterations = 100
  const k = Math.sqrt((viewportSize.value.width * viewportSize.value.height) / nodes.length)
  const repulsionStrength = k * k * 0.1

  for (let i = 0; i < iterations; i++) {
    for (let j = 0; j < nodes.length; j++) {
      for (let k = j + 1; k < nodes.length; k++) {
        const nodeA = nodes[j]
        const nodeB = nodes[k]

        const dx = nodeA.x! - nodeB.x!
        const dy = nodeA.y! - nodeB.y!
        const distance = Math.sqrt(dx * dx + dy * dy)

        if (distance > 0) {
          const force = repulsionStrength / distance
          const fx = (dx / distance) * force
          const fy = (dy / distance) * force

          nodeA.x! += fx
          nodeA.y! += fy
          nodeB.x! -= fx
          nodeB.y! -= fy
        }
      }
    }
  }

  // Update positions and connections
  nodes.forEach(node => {
    nodePositions.set(node.id, { x: node.x!, y: node.y! })
  })
  props.connections.forEach(connection => {
    const fromPos = nodePositions.get(connection.from)
    const toPos = nodePositions.get(connection.to)
    if (fromPos && toPos) {
      const path = calculateConnectionPath(fromPos, toPos)
      connectionPaths.set(`${connection.from}-${connection.to}`, path)
    }
  })

  isApplyingRepulsion.value = false
}

// 使用 requestAnimationFrame 优化布局计算
let layoutRAF: number | null = null

const scheduleLayout = () => {
  if (layoutRAF) {
    cancelAnimationFrame(layoutRAF)
  }
  layoutRAF = requestAnimationFrame(() => {
    updateViewportSize()
    layoutNodes()
    layoutRAF = null
  })
}

watch(() => [props.nodes, props.connections], () => {
  nextTick(() => {
    scheduleLayout()
  })
}, { immediate: true })

onMounted(() => {
  scheduleLayout()
  
  // 添加全局事件监听
  document.addEventListener('mousemove', handleMouseMove, { passive: true })
  document.addEventListener('mouseup', handleMouseUp, { passive: true })
  
  // 监听窗口大小变化
  window.addEventListener('resize', scheduleLayout)
})
</script>

<template>
  <div class="relative w-full h-full bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 overflow-hidden">
    <!-- Loading Indicator -->
    <div v-if="isApplyingRepulsion" class="absolute inset-0 bg-slate-800 bg-opacity-50 flex items-center justify-center z-20">
      <div class="text-white text-lg">优化布局中...</div>
    </div>

    <!-- 控制按钮 -->
    <div class="absolute top-2 sm:top-4 right-2 sm:right-4 z-10 flex flex-col space-y-1 sm:space-y-2">
      <button
        @click="resetView"
        class="p-1.5 sm:p-2 bg-white dark:bg-slate-700 rounded-lg shadow-md hover:shadow-lg transition-shadow text-slate-600 dark:text-slate-300 hover:text-slate-800 dark:hover:text-slate-100"
        title="重置视图"
      >
        <svg class="h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      </button>
      <button
        @click="applyRepulsion"
        class="p-1.5 sm:p-2 bg-white dark:bg-slate-700 rounded-lg shadow-md hover:shadow-lg transition-shadow text-slate-600 dark:text-slate-300 hover:text-slate-800 dark:hover:text-slate-100"
        title="优化布局"
      >
        <svg class="h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 16v-2m8-6h2M4 12H2m15.364 6.364l1.414 1.414M4.222 4.222l1.414 1.414m12.728 0l-1.414 1.414M5.636 18.364l-1.414 1.414" />
        </svg>
      </button>
      <div class="text-xs text-slate-500 dark:text-slate-400 bg-white dark:bg-slate-700 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded shadow">
        缩放: {{ scalePercentage }}%
      </div>
    </div>

    <svg
      ref="svgRef"
      class="w-full h-full cursor-grab active:cursor-grabbing"
      :viewBox="`0 0 ${viewportSize.width} ${viewportSize.height}`"
      @mousedown="handleMouseDown"
      @wheel="handleWheel"
    >
      <g :transform="`translate(${translate.x}, ${translate.y}) scale(${scale})`">
        <!-- 连接线 -->
        <g class="connections">
          <path
            v-for="connection in connections"
            :key="`${connection.from}-${connection.to}`"
            :d="getConnectionPath(connection)"
            :stroke="connection.type === 'strong' ? '#2563eb' : '#64748b'"
            :stroke-width="getConnectionWidth(connection)"
            :stroke-opacity="getConnectionOpacity(connection)"
            fill="none"
            stroke-dasharray="5,5"
            class="transition-all duration-300"
          />
        </g>

        <!-- 节点 -->
        <g class="nodes">
          <g
            v-for="node in nodes"
            :key="node.id"
            :transform="`translate(${node.x}, ${node.y})`"
            class="cursor-pointer transition-all duration-300"
            @click="selectNode(node)"
            @mouseenter="hoverNode(node)"
            @mouseleave="hoverNode(null)"
            @mousedown="(e) => handleMouseDown(e, node)"
          >
            <!-- 节点圆圈 -->
            <circle
              :r="getNodeSize(node.level)"
              :fill="getNodeColor(node)"
              :stroke="nodeStrokeColor(node)"
              :stroke-width="nodeStrokeWidth(node)"
              :opacity="nodeOpacity(node)"
              class="transition-all duration-300"
            />
            
            <!-- 节点文本 -->
            <text
              :font-size="Math.max(12 - node.level, 9)"
              text-anchor="middle"
              dy="0.35em"
              fill="white"
              font-weight="600"
              class="pointer-events-none select-none"
            >
              {{ node.title.length > 10 ? node.title.substring(0, 10) + '...' : node.title }}
            </text>
            
            <!-- 层级指示器 -->
            <circle
              v-if="node.level > 0"
              :r="4"
              :cx="getNodeSize(node.level) - 8"
              :cy="-getNodeSize(node.level) + 8"
              :fill="getNodeColor(node)"
              stroke="white"
              stroke-width="2"
            />
            <text
              v-if="node.level > 0"
              :x="getNodeSize(node.level) - 8"
              :y="-getNodeSize(node.level) + 8"
              font-size="8"
              text-anchor="middle"
              dy="0.25em"
              fill="white"
              font-weight="bold"
              class="pointer-events-none select-none"
            >
              {{ node.level }}
            </text>
          </g>
        </g>
      </g>
    </svg>

    <!-- 节点详情面板 -->
    <div
      v-if="selectedNode"
      class="absolute top-2 sm:top-4 left-2 sm:left-4 bg-white dark:bg-slate-800 p-3 sm:p-4 rounded-lg shadow-lg border border-slate-200 dark:border-slate-700 max-w-xs z-10"
    >
      <div class="flex items-start justify-between mb-2">
        <h4 class="font-semibold text-sm sm:text-base text-slate-800 dark:text-slate-200 pr-2">{{ selectedNode.title }}</h4>
        <button
          @click="selectedNode = null"
          class="text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 flex-shrink-0"
        >
          <svg class="h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      <p class="text-xs sm:text-sm text-slate-600 dark:text-slate-400 mb-1">类型: {{ selectedNodeModelName }}</p>
      <p class="text-xs sm:text-sm text-slate-600 dark:text-slate-400 mb-3">{{ selectedNode.description }}</p>
      
      <!-- 洞察 -->
      <div v-if="selectedNode.insights?.length" class="mb-3">
        <h5 class="text-xs font-medium text-slate-700 dark:text-slate-300 mb-1">关键洞察</h5>
        <div class="space-y-1">
          <div v-for="insight in selectedNode.insights" :key="insight" class="text-xs text-slate-600 dark:text-slate-400 bg-blue-50/80 dark:bg-blue-900/20 p-1 rounded">
            {{ insight }}
          </div>
        </div>
      </div>
      
      <div class="flex items-center justify-between text-xs text-slate-500 dark:text-slate-400">
        <span>层级: {{ selectedNode.level }}</span>
        <span>影响度: {{ selectedNode.impact }}</span>
      </div>
    </div>

    <!-- 图例 -->
    <div class="absolute bottom-2 sm:bottom-4 right-2 sm:right-4 bg-white dark:bg-slate-800 p-2 sm:p-3 rounded-lg shadow-lg border border-slate-200 dark:border-slate-700">
      <h5 class="text-xs font-semibold text-slate-700 dark:text-slate-300 mb-2">图例</h5>
      <div class="space-y-1">
        <div v-for="model in modelDetails" :key="model.id" class="flex items-center text-xs">
          <div class="w-2 h-2 sm:w-3 sm:h-3 rounded-full mr-1 sm:mr-2" :style="{ backgroundColor: model.color }"></div>
          <span class="text-slate-600 dark:text-slate-400 truncate">{{ model.name }}</span>
        </div>
      </div>
      <div class="mt-2 pt-2 border-t border-slate-200 dark:border-slate-600 text-xs text-slate-500 dark:text-slate-400 space-y-0.5">
        <div>💡 可拖拽节点</div>
        <div>🔍 滚轮缩放</div>
        <div>👆 拖拽平移</div>
      </div>
    </div>
  </div>
</template>