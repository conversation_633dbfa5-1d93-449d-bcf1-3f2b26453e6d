<script setup lang="ts">
import { language, toggleLanguage } from '../stores/language'
</script>

<template>
  <button
    @click="toggleLanguage"
    class="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-600 dark:text-slate-300 transition-colors duration-200"
    title="Toggle Language"
  >
    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 018.648 16H14m-5-6h.01M17 13l2-2m0 0l2 2m-2-2v6.5M10 16H6a2 2 0 01-2-2V6a2 2 0 012-2h5l2 2v5m-6 2h6" />
    </svg>
    <span class="hidden sm:inline text-sm font-medium">{{ language === 'zh' ? 'English' : '中文' }}</span>
  </button>
</template>

<style scoped>
/* 简洁的按钮样式 */
</style>