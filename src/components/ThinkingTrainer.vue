<script setup lang="ts">
import { ref, computed, onMounted, shallowRef } from 'vue'

import { language } from '../stores/language'
import QuestionCard from './QuestionCard.vue'
import ModelSettings from './ModelSettings.vue'
import HistoryPanel from './HistoryPanel.vue'
import ThemeToggle from './ThemeToggle.vue'
import LanguageToggle from './LanguageToggle.vue'

import { generateQuestions, type Question, type QuestionCategory } from '../utils/questionGenerator'
import { GeminiQuestionGenerator, getActiveModelConfig } from '../utils/geminiApi'
import { isLoading, startLoading, stopLoading } from '../utils/loadingState'

const inputText = ref('')
const questions = shallowRef<Question[]>([])
const selectedCategory = ref<QuestionCategory | 'all'>('all')
const error = ref('')
const showHistory = ref(false)
const inputFocused = ref(false)

// 历史记录
interface HistoryRecord {
  id: string
  topic: string
  questions: Question[]
  timestamp: number
}

const history = shallowRef<HistoryRecord[]>([])

// 性能优化：使用计算属性缓存
const filteredQuestions = computed(() => {
  if (selectedCategory.value === 'all') {
    return questions.value
  }
  return questions.value.filter(q => q.category === selectedCategory.value)
})

const hasQuestions = computed(() => questions.value.length > 0)

const hasActiveModel = computed(() => {
  const activeModel = getActiveModelConfig()
  return activeModel?.apiKey ? true : false
})

const translations = computed(() => {
  return {
    searchInputPlaceholder: language.value === 'zh' ? '输入任何实体、概念或主题...' : 'Enter any entity, concept, or topic...',
    history: language.value === 'zh' ? '历史' : 'History',
    categories: language.value === 'zh' ?
      [
        { value: 'all' as const, label: '全部', color: 'bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300' },
        { value: 'critical' as const, label: '批判性思维', color: 'bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300' },
        { value: 'creative' as const, label: '创意思维', color: 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300' },
        { value: 'logical' as const, label: '逻辑分析', color: 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' },
        { value: 'system' as const, label: '系统思维', color: 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300' },
        { value: 'ethical' as const, label: '伦理思考', color: 'bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300' },
        { value: 'practical' as const, label: '实践应用', color: 'bg-teal-100 dark:bg-teal-900/20 text-teal-700 dark:text-teal-300' }
      ] :
      [
        { value: 'all' as const, label: 'All', color: 'bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300' },
        { value: 'critical' as const, label: 'Critical Thinking', color: 'bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300' },
        { value: 'creative' as const, label: 'Creative Thinking', color: 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300' },
        { value: 'logical' as const, label: 'Logical Analysis', color: 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' },
        { value: 'system' as const, label: 'System Thinking', color: 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300' },
        { value: 'ethical' as const, label: 'Ethical Consideration', color: 'bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300' },
        { value: 'practical' as const, label: 'Practical Application', color: 'bg-teal-100 dark:bg-teal-900/20 text-teal-700 dark:text-teal-300' }
      ]
  }
})

// 分类数据优化：使用计算属性缓存
const categories = computed(() => translations.value.categories)


// 从localStorage加载历史记录
onMounted(() => {
  loadHistory()
  
  // 监听历史记录更新事件
  window.addEventListener('history-updated', loadHistory)
})

const loadHistory = () => {
  const savedHistory = localStorage.getItem('thinking-history')
  if (savedHistory) {
    try {
      history.value = JSON.parse(savedHistory)
    } catch (error) {
      console.error('Failed to load history:', error)
      history.value = []
    }
  }
}

const saveToHistory = (topic: string, generatedQuestions: Question[]) => {
  const record: HistoryRecord = {
    id: `history-${Date.now()}`,
    topic,
    questions: generatedQuestions,
    timestamp: Date.now()
  }
  
  history.value = [record, ...history.value]
  
  // 只保留最近50条记录
  if (history.value.length > 50) {
    history.value = history.value.slice(0, 50)
  }
  
  localStorage.setItem('thinking-history', JSON.stringify(history.value))
}

const handleGenerate = async () => {
  if (!inputText.value.trim()) return
  
  error.value = ''
  startLoading('Generating with AI...', hasActiveModel.value ? 1 : 0)
  
  try {
    let generatedQuestions
    
    // 获取当前激活的模型配置
    const activeModel = getActiveModelConfig()
    
    if (activeModel?.apiKey) {
      // 使用配置的 AI 模型生成问题
      const generator = new GeminiQuestionGenerator(activeModel.apiKey, activeModel)
      generatedQuestions = await generator.generateQuestions(inputText.value.trim())
    } else {
      // 使用本地模板生成问题
      await new Promise(resolve => setTimeout(resolve, 1200))
      generatedQuestions = generateQuestions(inputText.value.trim())
    }
    
    questions.value = generatedQuestions
    
    // 保存到历史记录
    saveToHistory(inputText.value.trim(), generatedQuestions)
    
  } catch (err) {
    console.error('Failed to generate questions:', err)
    error.value = err instanceof Error ? err.message : 'Unknown error occurred while generating questions'
    
    // 如果API失败，回退到本地生成
    const activeModel = getActiveModelConfig()
    if (activeModel?.apiKey) {
      try {
        await new Promise(resolve => setTimeout(resolve, 800))
        const fallbackQuestions = generateQuestions(inputText.value.trim())
        questions.value = fallbackQuestions
        saveToHistory(inputText.value.trim(), fallbackQuestions)
        error.value += ' (Generated questions using local template)'
      } catch (fallbackErr) {
        console.error('Local generation also failed:', fallbackErr)
      }
    }
  } finally {
    stopLoading()
  }
}

const handleClear = () => {
  inputText.value = ''
  questions.value = []
  selectedCategory.value = 'all'
  error.value = ''
}





const loadFromHistory = (record: HistoryRecord) => {
  inputText.value = record.topic
  questions.value = [] // 先清空
  questions.value = [...record.questions] // 再赋值
  selectedCategory.value = 'all'
  showHistory.value = false
}

const toggleHistory = () => {
  showHistory.value = !showHistory.value
}

// 输入框焦点处理
const handleInputFocus = () => {
  inputFocused.value = true
}

const handleInputBlur = () => {
  inputFocused.value = false
}

// 性能优化：防抖处理分类切换
let categoryTimeout: number | null = null
const handleCategoryChange = (category: QuestionCategory | 'all') => {
  if (categoryTimeout) {
    clearTimeout(categoryTimeout)
  }
  categoryTimeout = setTimeout(() => {
    selectedCategory.value = category
  }, 50) as any
}

// 获取分类问题数量（缓存计算）
const getCategoryCount = (category: QuestionCategory | 'all') => {
  if (category === 'all') return questions.value.length
  return questions.value.filter(q => q.category === category).length
}
</script>

<template>
  <div class="bg-slate-50 dark:bg-slate-900 flex flex-col transition-colors duration-200 min-h-screen">
    <div class="flex-1 pb-16 sm:pb-20">
      <div class="container mx-auto px-3 sm:px-4 max-w-4xl">
        <!-- 居中的搜索区域 -->
        <div :class="[
          'transition-all duration-300 ease-out flex flex-col items-center',
          hasQuestions
            ? 'pt-8 sm:pt-16 pb-8 sm:pb-12'
            : 'pt-24 sm:pt-40 pb-12 sm:pb-20'
        ]">
          <!-- 品牌标题 -->
          <div v-if="!hasQuestions" class="text-center mb-8 sm:mb-12">
            <h1 class="text-3xl sm:text-5xl font-bold text-slate-900 dark:text-slate-100 mb-4">
              ThinkFlash
            </h1>
            <p class="text-lg sm:text-xl text-slate-600 dark:text-slate-400 max-w-2xl">
              {{ language === 'zh' ? '激发深度思考' : 'Inspiring Deep Thinking' }}
            </p>
          </div>

          <!-- 搜索框 -->
          <div class="w-full max-w-2xl">
            <div class="relative">
              <!-- 彩虹边框容器 -->
              <div class="rainbow-border-container" :class="{ 'active': isInputFocused }">
                <input
                  v-model="inputText"
                  type="text"
                  :placeholder="translations.searchInputPlaceholder"
                  class="w-full px-6 py-4 text-lg bg-white dark:bg-slate-800 border-0 rounded-lg outline-none text-slate-900 dark:text-slate-100 placeholder-slate-500 dark:placeholder-slate-400 shadow-sm focus:shadow-md transition-shadow duration-200 relative z-10"
                  @keyup.enter="handleGenerate"
                  @focus="handleInputFocus"
                  @blur="handleInputBlur"
                />
              </div>

              <!-- 搜索按钮和清除按钮 -->
              <div class="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
                <button
                  v-if="inputText.trim() && !isLoading"
                  @click="handleClear"
                  class="p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700 text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 transition-colors duration-200"
                  title="Clear"
                >
                  <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>

                <button
                  @click="handleGenerate"
                  :disabled="!inputText.trim() || isLoading"
                  :class="[
                    'px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2',
                    isLoading
                      ? 'bg-slate-500 text-white cursor-wait'
                      : inputText.trim()
                        ? 'bg-slate-900 hover:bg-slate-800 dark:bg-slate-100 dark:hover:bg-slate-200 text-white dark:text-slate-900'
                        : 'bg-slate-200 dark:bg-slate-700 text-slate-400 dark:text-slate-500 cursor-not-allowed'
                  ]"
                  title="Generate Questions"
                >
                  <div v-if="isLoading" class="loading-dots">
                    <div></div>
                    <div></div>
                    <div></div>
                  </div>
                  <template v-else>
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <span>{{ language === 'zh' ? '思考' : 'Think' }}</span>
                  </template>
                </button>
              </div>
            </div>


          </div>
          
          <!-- 问题分类筛选 -->
          <div v-if="hasQuestions" class="mt-8 w-full">
            <!-- 移动端：横向滚动 -->
            <div class="sm:hidden overflow-x-auto pb-2">
              <div class="flex space-x-2 px-1 min-w-max">
                <button
                  v-for="cat in categories"
                  :key="cat.value"
                  @click="handleCategoryChange(cat.value)"
                  :class="[
                    'px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 whitespace-nowrap border',
                    selectedCategory === cat.value
                      ? 'bg-slate-900 dark:bg-slate-100 text-white dark:text-slate-900 border-slate-900 dark:border-slate-100'
                      : 'bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-300 border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-700'
                  ]"
                >
                  {{ cat.label }} ({{ getCategoryCount(cat.value) }})
                </button>
              </div>
            </div>

            <!-- 桌面端：居中换行 -->
            <div class="hidden sm:flex flex-wrap justify-center gap-2">
              <button
                v-for="cat in categories"
                :key="cat.value"
                @click="handleCategoryChange(cat.value)"
                :class="[
                  'px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 border',
                  selectedCategory === cat.value
                    ? 'bg-slate-900 dark:bg-slate-100 text-white dark:text-slate-900 border-slate-900 dark:border-slate-100'
                    : 'bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-300 border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-700'
                  ]"
              >
                {{ cat.label }} ({{ getCategoryCount(cat.value) }})
              </button>
            </div>
          </div>

          <!-- 错误信息 -->
          <div v-if="error" class="mt-4 text-red-500 dark:text-red-400 text-center text-sm px-4">
            {{ error }}
          </div>

          <!-- 问题列表 -->
          <div v-if="hasQuestions" class="mt-6 sm:mt-10 w-full">
            <!-- 移动端：单列布局 -->
            <div class="sm:hidden space-y-4">
              <QuestionCard 
                v-for="(question, index) in filteredQuestions" 
                :key="question.id || index" 
                :question="question" 
              />
            </div>
            
            <!-- 桌面端：网格布局 -->
            <div class="hidden sm:grid grid-cols-[repeat(auto-fit,minmax(280px,1fr))] gap-6">
              <QuestionCard 
                v-for="(question, index) in filteredQuestions" 
                :key="question.id || index" 
                :question="question" 
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 历史记录侧边栏 -->
    <HistoryPanel :show="showHistory" :history="history" @close="toggleHistory" @load-history="loadFromHistory" />

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 w-full bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-t border-slate-200 dark:border-slate-700 py-3 px-4 z-30">
      <div class="container mx-auto max-w-4xl">
        <!-- 移动端：紧凑布局 -->
        <div class="sm:hidden flex justify-between items-center">
          <div class="flex items-center space-x-2">
            <ThemeToggle />
            <LanguageToggle />
          </div>
          <div class="flex items-center space-x-2">
            <button
              @click="toggleHistory"
              class="p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-600 dark:text-slate-300 transition-colors duration-200"
              :title="language === 'zh' ? '历史记录' : 'History'"
            >
              <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
            <ModelSettings />
          </div>
        </div>

        <!-- 桌面端：居中布局 -->
        <div class="hidden sm:flex justify-center items-center space-x-4">
          <ThemeToggle />
          <LanguageToggle />
          <button
            @click="toggleHistory"
            class="flex items-center space-x-2 p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-600 dark:text-slate-300 transition-colors duration-200"
            :title="language === 'zh' ? '历史记录' : 'History'"
          >
            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span class="hidden lg:inline">{{ language === 'zh' ? '历史' : 'History' }}</span>
          </button>
          <ModelSettings />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 简洁的滚动条样式 */
.overflow-x-auto::-webkit-scrollbar {
  height: 2px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 1px;
}

/* Apple Intelligence 风格的彩虹边框 */
.rainbow-border-container {
  position: relative;
  border-radius: 0.5rem;
  padding: 2px;
  background: linear-gradient(45deg, #e2e8f0, #e2e8f0);
  transition: all 0.3s ease;
}

.rainbow-border-container.active {
  background: linear-gradient(
    45deg,
    #ff6b6b,
    #4ecdc4,
    #45b7d1,
    #96ceb4,
    #feca57,
    #ff9ff3,
    #54a0ff,
    #5f27cd,
    #ff6b6b
  );
  background-size: 400% 400%;
  animation: rainbow-flow 3s ease-in-out infinite;
}

.rainbow-border-container.active::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: white;
  border-radius: calc(0.5rem - 2px);
  z-index: 1;
}

.dark .rainbow-border-container.active::before {
  background: rgb(30 41 59); /* slate-800 */
}

@keyframes rainbow-flow {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}
</style>