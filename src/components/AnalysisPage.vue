<script setup lang="ts">
import { ref, onMounted, shallowRef, computed } from 'vue'
import { language } from '../stores/language'

import NetworkGraph from './NetworkGraph.vue'
import ThinkingModelSelector from './ThinkingModelSelector.vue'
import { generateSystemicAnalysis, loadAnalysisResult, type ThinkingModel, type AnalysisResult } from '../utils/systemicThinking'
import { isLoading, loadingMessage, currentLoadingStep, totalLoadingSteps, startLoading, stopLoading, updateLoadingProgress } from '../utils/loadingState'

const topic = ref('')
const thinkingDepth = ref(2)
const thinkingContext = ref('')
const selectedModels = ref<ThinkingModel[]>(['butterfly-effect', 'premortem', 'red-team'])
const analysisResult = shallowRef<AnalysisResult | null>(null)
const analysisError = ref('')
const isLeftSidebarCollapsed = ref(false)
const isRightSidebarCollapsed = ref(false)

const translations = computed(() => {
  const modelDescriptions = {
    'butterfly-effect': language.value === 'zh' ? '蝴蝶效应' : 'Butterfly Effect Analysis',
    'premortem': language.value === 'zh' ? '事前验尸' : 'Pre-mortem Analysis',
    'red-team': language.value === 'zh' ? '红队模拟' : 'Red Team Simulation Analysis',
    'systems-thinking': language.value === 'zh' ? '系统思维' : 'Systems Thinking Analysis',
    'scenario-planning': language.value === 'zh' ? '情景规划' : 'Scenario Planning Analysis',
    'root-cause': language.value === 'zh' ? '根因分析' : 'Root Cause Analysis'
  };

  const depthLabels = {
    1: language.value === 'zh' ? '浅层分析' : 'Shallow Analysis',
    2: language.value === 'zh' ? '标准分析' : 'Standard Analysis',
    3: language.value === 'zh' ? '深度分析' : 'Deep Analysis',
    4: language.value === 'zh' ? '全面分析' : 'Comprehensive Analysis',
    5: language.value === 'zh' ? '极深分析' : 'Extensive Analysis'
  };

  return {
    pageTitle: language.value === 'zh' ? '系统化分析' : 'Systemic Analysis',
    topicPrefix: language.value === 'zh' ? '问题:' : 'Topic: ',
    exportAnalysis: language.value === 'zh' ? '导出分析结果' : 'Export Analysis Result',
    export: language.value === 'zh' ? '导出' : 'Export',
    analysisConfig: language.value === 'zh' ? '分析配置' : 'Analysis Configuration',
    thinkingDepth: language.value === 'zh' ? '思考深度：' : 'Thinking Depth:',
    depthShallow: depthLabels[1],
    depthStandard: depthLabels[2],
    depthDeep: depthLabels[3],
    depthComprehensive: depthLabels[4],
    depthExtensive: depthLabels[5],
    startAnalysis: language.value === 'zh' ? '开始系统化分析' : 'Start AI Systemic Analysis',
    reAnalyze: language.value === 'zh' ? '重新分析' : 'Re-Analyze',
    analyzing: language.value === 'zh' ? '模型分析中...' : 'AI Analyzing...',
    analysisFailed: language.value === 'zh' ? '分析失败' : 'Analysis Failed',
    retry: language.value === 'zh' ? '重试' : 'Retry',
    configureAnalysis: language.value === 'zh' ? '配置分析参数' : 'Configure Analysis Parameters',
    configureAnalysisPrompt: language.value === 'zh' ? '请选择思维模型和分析深度，然后点击"开始系统化分析"按钮' : 'Please select thinking models and analysis depth, then click "Start Systemic Analysis" button',
    analysisTips: language.value === 'zh' ? '分析提示' : 'Analysis Tips',
    tip1: language.value === 'zh' ? '选择多个思维模型可获得更全面的分析' : 'Selecting multiple thinking models provides a more comprehensive analysis',
    tip2: language.value === 'zh' ? '思考深度越高，分析越详细但耗时更长' : 'Higher thinking depth leads to more detailed but time-consuming analysis',
    tip3: language.value === 'zh' ? '建议首次使用选择2-3个模型，标准深度' : 'For first use, it is recommended to select 2-3 models with standard depth',
    analysisDetails: language.value === 'zh' ? '分析详情' : 'Analysis Details',
    analysisNodes: language.value === 'zh' ? '分析节点' : 'Analysis Nodes',
    connections: language.value === 'zh' ? '关联连接' : 'Connections',
    totalTokens: language.value === 'zh' ? '总Token' : 'Total Tokens',
    tokensPerSecond: language.value === 'zh' ? 'Token/秒' : 'Tokens/Second',
    keyInsights: language.value === 'zh' ? '关键洞察' : 'Key Insights',
    nodeDetails: language.value === 'zh' ? '节点详情' : 'Node Details',
    nodeId: language.value === 'zh' ? 'ID:' : 'ID:',
    nodeType: language.value === 'zh' ? '类型:' : 'Type:',
    nodeDescription: language.value === 'zh' ? '描述:' : 'Description:',
    nodeProperties: language.value === 'zh' ? '属性:' : 'Properties:',
    detailedAnalysis: language.value === 'zh' ? '详细分析' : 'Detailed Analysis',
    keyFindings: language.value === 'zh' ? '关键发现:' : 'Key Findings:',
    recommendations: language.value === 'zh' ? '建议:' : 'Recommendations:',
    unknownCategory: language.value === 'zh' ? '未知类别' : 'Unknown Category',
    unknownDifficulty: language.value === 'zh' ? '未知难度' : 'Unknown Difficulty',
    totalRecords: language.value === 'zh' ? '条记录' : 'records',
    questions: language.value === 'zh' ? '个问题' : 'questions',
    clearAll: language.value === 'zh' ? '清空全部' : 'Clear All',
    delete: language.value === 'zh' ? '删除' : 'Delete',
    viewDetails: language.value === 'zh' ? '查看详情' : 'View Details',
    loadUse: language.value === 'zh' ? '加载使用' : 'Load & Use',
    noMatchingRecords: language.value === 'zh' ? '没有找到匹配的记录' : 'No matching records found',
    noHistoryYet: language.value === 'zh' ? '暂无历史记录' : 'No history yet',
    tryDifferentKeywords: language.value === 'zh' ? '尝试使用其他关键词搜索' : 'Try searching with different keywords',
    historyWillAppear: language.value === 'zh' ? '开始生成问题后，历史记录会显示在这里' : 'History will appear here after you generate questions',
    exporting: language.value === 'zh' ? '导出中...' : 'Exporting...',
    exportFailed: language.value === 'zh' ? '导出失败，请重试' : 'Export failed, please try again',
    confirmDelete: language.value === 'zh' ? '确定要删除这条历史记录吗？' : 'Are you sure you want to delete this history record?',
    confirmClearAll: language.value === 'zh' ? '确定要清空所有历史记录吗？此操作不可恢复。' : 'Are you sure you want to clear all history records? This action cannot be undone?',
    systemAnalysisReport: language.value === 'zh' ? '系统化分析报告' : 'Systemic Analysis Report',
    analysisTopic: language.value === 'zh' ? '分析主题' : 'Analysis Topic',
    analysisTime: language.value === 'zh' ? '分析时间' : 'Analysis Time',
    thinkingDepthLabel: language.value === 'zh' ? '思考深度' : 'Thinking Depth',
    usedModels: language.value === 'zh' ? '使用模型' : 'Used Models',
    analysisOverview: language.value === 'zh' ? '分析概况' : 'Analysis Overview',
    nodeTitle: language.value === 'zh' ? '节点标题' : 'Node Title',
    questionCategory: language.value === 'zh' ? '类别' : 'Category',
    questionDifficulty: language.value === 'zh' ? '难度' : 'Difficulty',
    estimatedTime: language.value === 'zh' ? '预估时间' : 'Estimated Time',
    questionDescription: language.value === 'zh' ? '描述' : 'Description',
    thinkingHints: language.value === 'zh' ? '思考提示' : 'Thinking Hints',
    thinkingContextLabel: language.value === 'zh' ? '思考背景' : 'Thinking Context',
    thinkingContextPlaceholder: language.value === 'zh' ? '输入背景信息...' : 'Enter background information...',
    currentLanguage: language.value,
    modelDescriptions: modelDescriptions,
    depthLabels: depthLabels
  };
});

// 性能优化：计算属性缓存
const hasValidConfig = computed(() => selectedModels.value.length > 0)
const canStartAnalysis = computed(() => topic.value.trim() && hasValidConfig.value && !isLoading.value)

onMounted(() => {
  // 从URL参数获取主题
  const urlParams = new URLSearchParams(window.location.search)
  const topicParam = urlParams.get('topic')
  if (topicParam) {
    topic.value = decodeURIComponent(topicParam)
    // 尝试加载之前保存的分析结果
    const savedAnalysis = loadAnalysisResult(topic.value)
    if (savedAnalysis) {
      analysisResult.value = savedAnalysis
    }
  }
})

const startAnalysis = async () => {
  if (!canStartAnalysis.value) return
  
  startLoading('AI分析中...', selectedModels.value.length)
  analysisError.value = ''
  analysisResult.value = null // 清除之前的结果
  
  try {
    analysisResult.value = await generateSystemicAnalysis(
      topic.value,
      selectedModels.value,
      thinkingDepth.value,
      thinkingContext.value,
      (step: number) => {
        updateLoadingProgress(step)
      }
    )
  } catch (error) {
    console.error('分析失败:', error)
    analysisError.value = error instanceof Error ? error.message : '分析失败，请重试'
  } finally {
    stopLoading()
    isControlPanelCollapsed.value = true
  }
}

const closeWindow = () => {
  window.close()
}

const exportAnalysis = () => {
  if (!analysisResult.value) return

  let htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>系统化分析报告 - ${topic.value}</title>
      <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; line-height: 1.6; color: #333; }
        h1, h2, h3, h4 { color: #2563eb; }
        h1 { border-bottom: 2px solid #2563eb; padding-bottom: 10px; margin-bottom: 20px; }
        h2 { margin-top: 30px; border-bottom: 1px solid #eee; padding-bottom: 5px; }
        h3 { margin-top: 20px; color: #1e40af; }
        .section { margin-bottom: 20px; padding: 15px; background: #f8fafc; border-radius: 8px; border: 1px solid #eee; }
        .section-dark { background: #e0f2fe; }
        ul { list-style-type: disc; margin-left: 20px; }
        ol { margin-left: 20px; }
        .node-card { background: #fff; border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.05); }
        .node-card h4 { margin-top: 0; margin-bottom: 5px; color: #059669; }
        .node-card p { margin-bottom: 5px; font-size: 0.9em; }
        .node-card strong { color: #333; }
        .model-result { background: #f0f9ff; border: 1px solid #bfdbfe; border-radius: 8px; padding: 15px; margin-bottom: 15px; }
        .model-result h4 { color: #059669; }
        .model-result ul { list-style-type: circle; }
      </style>
    </head>
    <body>
      <h1>系统化分析报告</h1>
      <div class="section">
        <p><strong>分析主题:</strong> ${topic.value}</p>
        <p><strong>分析时间:</strong> ${new Date().toLocaleString('zh-CN')}</p>
        <p><strong>思考深度:</strong> ${translations.value.depthLabels[thinkingDepth.value as keyof typeof translations.value.depthLabels]}</p>
        <p><strong>使用模型:</strong> ${selectedModels.value.join(', ')}</p>
      </div>

      <h2>📊 分析概况</h2>
      <div class="section section-dark">
        <p><strong>分析节点:</strong> ${analysisResult.value.summary.totalNodes} 个</p>
        <p><strong>关联连接:</strong> ${analysisResult.value.summary.totalConnections} 个</p>
      </div>
  `

  if (analysisResult.value.summary.keyInsights.length > 0) {
    htmlContent += `
      <h2>💡 关键洞察</h2>
      <div class="section">
        <ul>
          ${analysisResult.value.summary.keyInsights.map(insight => `<li>${insight}</li>`).join('')}
        </ul>
      </div>
    `
  }

  htmlContent += `
    <h2>节点详情</h2>
    <div class="section">
      ${analysisResult.value.nodes.map(node => `
        <div class="node-card">
          <h4>${node.title}</h4>
          <p><strong>ID:</strong> ${node.id}</p>
          <p><strong>类型:</strong> ${translations.value.modelDescriptions[node.modelSource as ThinkingModel] || node.modelSource}</p>
          ${node.description ? `<p><strong>描述:</strong> ${node.description}</p>` : ''}
          ${node.properties ? `<p><strong>属性:</strong> ${JSON.stringify(node.properties)}</p>` : ''}
        </div>
      `).join('')}
    </div>
  `

  htmlContent += `
    <h2>🔍 详细分析</h2>
    ${Object.entries(analysisResult.value.modelResults).map(([, modelResult]) => {
      if (modelResult) {
        return `
          <div class="model-result">
            <h3>${modelResult.name}</h3>
            <p>${modelResult.description}</p>
            ${modelResult.keyFindings.length > 0 ? `
              <h4>关键发现:</h4>
              <ul>
                ${modelResult.keyFindings.map(finding => `<li>${finding}</li>`).join('')}
              </ul>
            ` : ''}
            ${modelResult.recommendations.length > 0 ? `
              <h4>建议:</h4>
              <ul>
                ${modelResult.recommendations.map(rec => `<li>${rec}</li>`).join('')}
              </ul>
            ` : ''}
          </div>
        `
      }
      return ''
    }).join('')}
  `

  htmlContent += `</body></html>`

  const printWindow = window.open('', '_blank')
  if (printWindow) {
    printWindow.document.write(htmlContent)
    printWindow.document.close()
    printWindow.focus()
    setTimeout(() => {
      printWindow.print()
      printWindow.close()
    }, 500)
  }
}

const retryAnalysis = () => {
  analysisError.value = ''
  startAnalysis()
}
</script>

<template>
  <div class="h-screen flex flex-col bg-slate-50 dark:bg-slate-900 text-slate-800 dark:text-slate-200">
    <!-- Header -->
    <header class="flex-shrink-0 bg-white/70 dark:bg-slate-800/70 backdrop-blur-lg border-b border-slate-200 dark:border-slate-700 z-20">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center space-x-4">
            <h1 class="text-xl font-bold truncate">{{ translations.pageTitle }}</h1>
            <p class="text-sm text-slate-500 dark:text-slate-400 hidden md:block truncate">{{ topic }}</p>
          </div>
          <div class="flex items-center space-x-2">
            <button v-if="analysisResult" @click="exportAnalysis" class="btn-secondary-sm">
              <svg class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" /></svg>
              {{ translations.export }}
            </button>
            <button @click="$emit('close')" class="btn-icon">
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
            </button>
          </div>
        </div>
      </div>
    </header>

    <div class="flex-1 flex overflow-hidden relative">
      <!-- Left Sidebar (Control Panel) -->
      <aside :class="['flex-shrink-0 bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700 overflow-y-auto transition-all duration-300 ease-in-out', isLeftSidebarCollapsed ? 'w-0' : 'w-80 lg:w-96']">
        <div v-if="!isLeftSidebarCollapsed" class="p-6 space-y-6">
          <div class="space-y-4">
            <div>
              <label class="form-label">{{ translations.thinkingDepth }}</label>
              <input v-model="thinkingDepth" type="range" min="1" max="5" step="1" class="w-full slider" :disabled="isLoading" />
              <div class="flex justify-between text-xs text-slate-500 dark:text-slate-400 mt-1">
                <span>浅</span><span>标准</span><span>深</span><span>全面</span><span>极深</span>
              </div>
            </div>
            <div>
              <label class="form-label">{{ translations.thinkingContextLabel }}</label>
              <textarea v-model="thinkingContext" rows="4" class="form-textarea" :placeholder="translations.thinkingContextPlaceholder"></textarea>
            </div>
          </div>
          
          <ThinkingModelSelector v-model="selectedModels" :disabled="isLoading" />

          <div class="pt-6 border-t border-slate-200 dark:border-slate-700">
            <button @click="startAnalysis" :disabled="!canStartAnalysis" class="btn-primary w-full">
              <span v-if="isLoading" class="flex items-center justify-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                {{ loadingMessage }} ({{ currentLoadingStep }}/{{ totalLoadingSteps }})
              </span>
              <span v-else>{{ analysisResult ? '重新分析' : '开始分析' }}</span>
            </button>
          </div>

          <div v-if="analysisError" class="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 rounded-lg">
            <h4 class="text-sm font-semibold text-red-800 dark:text-red-300 mb-2">分析失败</h4>
            <p class="text-xs text-red-700 dark:text-red-400">{{ analysisError }}</p>
            <button @click="retryAnalysis" class="mt-3 text-xs font-medium text-red-600 dark:text-red-400 hover:underline">重试</button>
          </div>
        </div>
      </aside>
      <!-- Collapse Button for Left Sidebar -->
      <button
        @click="isLeftSidebarCollapsed = !isLeftSidebarCollapsed"
        :class="['absolute top-1/2 -translate-y-1/2 p-1.5 rounded-full bg-white dark:bg-slate-800 shadow-md border border-slate-200 dark:border-slate-700 transition-all duration-300 ease-in-out z-30', isLeftSidebarCollapsed ? 'left-0 ml-2' : 'left-80 lg:left-96 -ml-4']"
      >
        <svg :class="['w-5 h-5 text-slate-500 transition-transform duration-300', isLeftSidebarCollapsed ? 'rotate-180' : '']" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>

      <!-- Main Content -->
      <main class="flex-1 flex flex-col overflow-hidden">
        <div v-if="analysisResult" class="flex-1 flex flex-col lg:flex-row overflow-hidden">
          <div class="flex-1 relative p-2 sm:p-4">
            <NetworkGraph :nodes="analysisResult.nodes" :connections="analysisResult.connections" :thinking-depth="thinkingDepth" />
          </div>
          <!-- Wrapper for Right Sidebar and its button -->
          <div :class="['relative', isRightSidebarCollapsed ? 'w-0' : 'w-full lg:w-80', 'transition-all duration-300 ease-in-out']">
            <aside class="h-full bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border-t lg:border-t-0 lg:border-l border-slate-200 dark:border-slate-700 overflow-y-auto p-4 space-y-4">
              <div v-if="!isRightSidebarCollapsed">
                <h3 class="text-lg font-semibold">分析详情</h3>
                <div class="grid grid-cols-2 gap-4">
                  <div class="stat-card"><div class="stat-value">{{ analysisResult.summary.totalNodes }}</div><div class="stat-label">分析节点</div></div>
                  <div class="stat-card"><div class="stat-value">{{ analysisResult.summary.totalConnections }}</div><div class="stat-label">关联连接</div></div>
                  <div class="stat-card"><div class="stat-value">{{ analysisResult.summary.totalTokenCount || 0 }}</div><div class="stat-label">总Token</div></div>
                  <div class="stat-card"><div class="stat-value">{{ (analysisResult.summary.averageTokensPerSecond || 0).toFixed(0) }}</div><div class="stat-label">Token/秒</div></div>
                </div>
                <div>
                  <h4 class="font-semibold mb-2">{{ translations.keyInsights }}</h4>
                  <ul class="space-y-2">
                    <li v-for="insight in analysisResult.summary.keyInsights" :key="insight" class="insight-card">{{ insight }}</li>
                  </ul>
                </div>

                <div class="mt-6">
                  <h3 class="text-lg font-semibold mb-3">{{ translations.detailedAnalysis }}</h3>
                  <div v-for="([modelId, modelResult]) in Object.entries(analysisResult.modelResults)" :key="modelId" class="model-result mb-4 p-4 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800/50">
                    <h4 class="font-bold text-blue-700 dark:text-blue-300 mb-2">{{ modelResult.name }}</h4>
                    <p class="text-sm text-slate-600 dark:text-slate-400 mb-3">{{ modelResult.description }}</p>
                    <div v-if="modelResult.keyFindings && modelResult.keyFindings.length > 0" class="mb-3">
                      <h5 class="font-semibold text-slate-700 dark:text-slate-300 mb-1">{{ translations.keyFindings }}</h5>
                      <ul class="list-disc list-inside text-sm text-slate-600 dark:text-slate-400 space-y-1">
                        <li v-for="finding in modelResult.keyFindings" :key="finding">{{ finding }}</li>
                      </ul>
                    </div>
                    <div v-if="modelResult.recommendations && modelResult.recommendations.length > 0">
                      <h5 class="font-semibold text-slate-700 dark:text-slate-300 mb-1">{{ translations.recommendations }}</h5>
                      <ul class="list-disc list-inside text-sm text-slate-600 dark:text-slate-400 space-y-1">
                        <li v-for="rec in modelResult.recommendations" :key="rec">{{ rec }}</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </aside>
            <!-- Collapse Button for Right Sidebar -->
            <button
              @click="isRightSidebarCollapsed = !isRightSidebarCollapsed"
              :class="['absolute top-1/2 -translate-y-1/2 p-1.5 rounded-full bg-white dark:bg-slate-800 shadow-md border border-slate-200 dark:border-slate-700 transition-all duration-300 ease-in-out z-30', isRightSidebarCollapsed ? 'left-0 ml-2' : 'left-0 -ml-4']"
            >
              <svg :class="['w-5 h-5 text-slate-500 transition-transform duration-300', isRightSidebarCollapsed ? '' : 'rotate-180']" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          </div>
        </div>
        <div v-else-if="isLoading" class="flex-1 flex items-center justify-center text-center p-4">
          <div>
            <svg class="animate-spin mx-auto h-12 w-12 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
            <h3 class="mt-4 text-lg font-semibold">{{ loadingMessage }}</h3>
            <p class="mt-2 text-sm text-slate-500 dark:text-slate-400">正在运用多种思维模型进行深度分析...</p>
            <div class="w-64 bg-slate-200 dark:bg-slate-700 rounded-full h-2.5 mt-4 mx-auto"><div class="bg-blue-600 h-2.5 rounded-full" :style="{ width: `${totalLoadingSteps > 0 ? (currentLoadingStep / totalLoadingSteps) * 100 : 0}%` }"></div></div>
          </div>
        </div>
        <div v-else class="flex-1 flex items-center justify-center text-center p-4">
          <div class="max-w-lg">
            <svg class="mx-auto h-16 w-16 text-slate-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM18 13.5a3.375 3.375 0 00-3.375-3.375L14.25 9.75l.375 1.375a3.375 3.375 0 006.75 0L21.75 9.75l-.375-1.375A3.375 3.375 0 0018 13.5z" /></svg>
            <h3 class="mt-4 text-xl font-semibold">开始您的系统化分析</h3>
            <p class="mt-2 text-sm text-slate-500 dark:text-slate-400">在左侧配置面板中选择思维模型和思考深度，然后点击“开始分析”按钮，探索事物的无限可能性。</p>
            <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800/50 rounded-lg text-left">
              <h4 class="font-semibold text-blue-800 dark:text-blue-300">💡 分析提示</h4>
              <ul class="mt-2 text-xs text-blue-700 dark:text-blue-400 space-y-1.5">
                <li>• 选择多个模型可获得更全面的视角。</li>
                <li>• 思考深度越高，分析越详细，耗时也越长。</li>
                <li>• 首次使用建议选择2-3个模型和标准深度。</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>