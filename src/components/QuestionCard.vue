<script setup lang="ts">
import { ref } from 'vue'
import type { Question } from '../utils/questionGenerator'
import { renderMarkdown } from '../utils/markdownRenderer'

interface Props {
  question: Question
}

const props = defineProps<Props>()

const isFavorited = ref(false)
const isHovered = ref(false)
const mouseX = ref(0)
const mouseY = ref(0)

const handleMouseMove = (event: MouseEvent) => {
  const card = event.currentTarget as HTMLElement
  const rect = card.getBoundingClientRect()
  mouseX.value = event.clientX - rect.left
  mouseY.value = event.clientY - rect.top
}

const categoryStyles = {
  critical: 'bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 border-slate-200 dark:border-slate-700',
  creative: 'bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 border-slate-200 dark:border-slate-700',
  logical: 'bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 border-slate-200 dark:border-slate-700',
  system: 'bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 border-slate-200 dark:border-slate-700',
  ethical: 'bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 border-slate-200 dark:border-slate-700',
  practical: 'bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 border-slate-200 dark:border-slate-700'
}

const categoryLabels = {
  critical: '批判性思维',
  creative: '创意思维',
  logical: '逻辑分析',
  system: '系统思维',
  ethical: '伦理思考',
  practical: '实践应用'
}

const difficultyStyles = {
  beginner: 'bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 border-slate-200 dark:border-slate-700',
  intermediate: 'bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 border-slate-200 dark:border-slate-700',
  advanced: 'bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 border-slate-200 dark:border-slate-700'
}

const difficultyLabels = {
  beginner: '入门',
  intermediate: '中级',
  advanced: '高级'
}

const toggleFavorite = () => {
  isFavorited.value = !isFavorited.value
}

const startSystemicAnalysis = () => {
  // 打开系统分析页面，但不自动开始分析
  const analysisUrl = `/analysis?topic=${encodeURIComponent(props.question.question)}`
  window.open(analysisUrl, '_blank', 'width=1400,height=900,scrollbars=yes,resizable=yes')
}

const startRoundtableMeeting = () => {
  // 打开圆桌会议页面，传递问题作为主题和提示
  const roundtableUrl = `/roundtable?topic=${encodeURIComponent(props.question.question)}&hints=${encodeURIComponent(JSON.stringify(props.question.hints))}`
  window.open(roundtableUrl, '_blank', 'width=1400,height=900,scrollbars=yes,resizable=yes')
}
</script>

<template>
  <div class="question-card bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700">
    <div>
      <!-- 卡片头部 -->
      <div class="flex items-start justify-between mb-4">
        <div class="flex flex-wrap gap-2 flex-1 mr-3">
          <span :class="['px-3 py-1 rounded-lg text-sm font-medium border', categoryStyles[question.category]]">
            {{ categoryLabels[question.category] }}
          </span>
          <span :class="['px-3 py-1 rounded-lg text-sm font-medium border', difficultyStyles[question.difficulty]]">
            {{ difficultyLabels[question.difficulty] }}
          </span>
        </div>
        <button
          @click="toggleFavorite"
          :class="[
            'p-2 rounded-lg transition-colors duration-200 flex-shrink-0',
            isFavorited
              ? 'text-slate-900 dark:text-slate-100 bg-slate-100 dark:bg-slate-700'
              : 'text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800'
          ]"
        >
          <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
            <path
              :fill-rule="isFavorited ? 'evenodd' : 'nonzero'"
              d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
              :clip-rule="isFavorited ? 'evenodd' : 'nonzero'"
            />
          </svg>
        </button>
      </div>

      <!-- 问题内容 -->
      <div class="mb-6">
        <h4 class="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-3 leading-tight">
          {{ question.question }}
        </h4>
        <p class="text-slate-600 dark:text-slate-400 leading-relaxed"
          v-html="renderMarkdown(question.description)">
        </p>
      </div>

      <!-- 提示信息 -->
      <div v-if="question.hints.length > 0" class="mb-6">
        <h5 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3 flex items-center">
          <svg class="h-4 w-4 mr-2 text-slate-500 dark:text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          思考提示
        </h5>
        <ul class="space-y-2">
          <li
            v-for="hint in question.hints"
            :key="hint"
            class="text-sm text-slate-600 dark:text-slate-400 flex items-start"
          >
            <span class="text-slate-500 dark:text-slate-400 mr-2 mt-0.5">•</span>
            <span class="flex-1" v-html="renderMarkdown(hint)"></span>
          </li>
        </ul>
      </div>

      <!-- 卡片底部 -->
      <div class="flex items-center justify-between pt-4 border-t border-slate-200 dark:border-slate-700">
        <div class="flex items-center text-sm text-slate-500 dark:text-slate-400">
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {{ question.estimatedTime }}
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="startSystemicAnalysis"
            class="flex items-center space-x-2 text-sm font-medium text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-slate-100 bg-slate-100 dark:bg-slate-800 hover:bg-slate-200 dark:hover:bg-slate-700 px-4 py-2 rounded-lg transition-colors duration-200"
          >
            <span>系统分析</span>
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </button>
          <button
            @click="startRoundtableMeeting"
            class="flex items-center space-x-2 text-sm font-medium text-white dark:text-slate-900 bg-slate-900 dark:bg-slate-100 hover:bg-slate-800 dark:hover:bg-slate-200 px-4 py-2 rounded-lg transition-colors duration-200"
          >
            <span>圆桌会议</span>
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.714-4.03 8.5-9 8.5S3 16.714 3 12c0-4.714 4.03-8.5 9-8.5s9 3.786 9 8.5z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 简洁的卡片样式 */
</style>