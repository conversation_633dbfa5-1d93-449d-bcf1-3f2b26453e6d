import { ref } from 'vue';

import { ref } from 'vue';

export const isLoading = ref(false);
export const loadingMessage = ref('');
export const currentLoadingStep = ref(0);
export const totalLoadingSteps = ref(0);
export const currentLLMRoleName = ref(''); // New reactive variable for role name

export function startLoading(message: string = '加载中...', totalSteps: number = 0, roleName: string = '') {
  isLoading.value = true;
  loadingMessage.value = message;
  currentLoadingStep.value = 0;
  totalLoadingSteps.value = totalSteps;
  currentLLMRoleName.value = roleName; // Set role name
}

export function updateLoadingProgress(currentStep: number) {
  currentLoadingStep.value = currentStep;
}

export function stopLoading() {
  isLoading.value = false;
  loadingMessage.value = '';
  currentLoadingStep.value = 0;
  totalLoadingSteps.value = 0;
  currentLLMRoleName.value = ''; // Clear role name
}