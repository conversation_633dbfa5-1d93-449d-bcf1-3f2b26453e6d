import { getActiveModelConfig } from './geminiApi'

const CUSTOM_MODELS_STORAGE_KEY = 'custom-thinking-models';

export interface ThinkingModelDetail {
  id: 'butterfly-effect' | 'premortem' | 'red-team' | 'systems-thinking' | 'scenario-planning' | 'root-cause' | string;
  name: string;
  description: string;
  color: string;
  icon: string;
}

export type ThinkingModel = ThinkingModelDetail['id'];

export interface AnalysisNode {
  id: string
  title: string
  description: string
  level: number // 0=核心, 1=一阶, 2=二阶, etc.
  impact: 'low' | 'medium' | 'high' | 'critical'
  x?: number
  y?: number
  modelSource: ThinkingModel
  insights?: string[]
  properties?: any; // Added to resolve TS2339
}

export interface NodeConnection {
  from: string
  to: string
  type: 'weak' | 'strong'
  description: string
  strength: number // 0-1 连接强度
}

export interface ModelResult {
  name: string
  description: string
  insights: string[]
  nodes: AnalysisNode[]
  connections: NodeConnection[]
  keyFindings: string[]
  recommendations: string[]
  tokenCount?: number // 新增：模型消耗的token数量
  duration?: number // 新增：模型分析耗时 (ms)
  tokensPerSecond?: number // 新增：分析速度 (tokens/s)
}

export interface AnalysisResult {
  nodes: AnalysisNode[]
  connections: NodeConnection[]
  modelResults: { [key in ThinkingModel]?: ModelResult }
  summary: {
    totalNodes: number
    totalConnections: number
    keyInsights: string[]
    criticalFactors: string[]
    totalTokenCount?: number // 新增：总token数量
    totalDuration?: number // 新增：总耗时 (ms)
    averageTokensPerSecond?: number // 新增：平均分析速度 (tokens/s)
  }
}

// LLM驱动的系统分析生成器
export const defaultThinkingModels: ThinkingModelDetail[] = [
  {
    id: 'butterfly-effect',
    name: '蝴蝶效应分析',
    description: '分析微小变化可能产生的巨大连锁反应',
    color: '#9333ea', // purple-600
    icon: '🦋'
  },
  {
    id: 'premortem',
    name: '事前验尸',
    description: '预先分析可能的失败原因和风险点',
    color: '#dc2626', // red-600
    icon: '🔍'
  },
  {
    id: 'red-team',
    name: '红队模拟',
    description: '从对立角度挑战和质疑现有观点',
    color: '#ea580c', // orange-600
    icon: '⚔️'
  },
  {
    id: 'systems-thinking',
    name: '系统思维',
    description: '分析各要素间的相互关系和整体效应',
    color: '#2563eb', // blue-600
    icon: '🔗'
  },
  {
    id: 'scenario-planning',
    name: '情景规划',
    description: '构建多种可能的未来情景和应对策略',
    color: '#16a34a', // green-600
    icon: '🎯'
  },
  {
    id: 'root-cause',
    name: '根因分析',
    description: '深入挖掘问题的根本原因',
    color: '#0d9488', // teal-600
    icon: '🌱'
  }
];

export function loadCustomModels(): ThinkingModelDetail[] {
  const saved = localStorage.getItem(CUSTOM_MODELS_STORAGE_KEY);
  if (saved) {
    try {
      return JSON.parse(saved);
    } catch (error) {
      console.error('Failed to load custom models:', error);
      return [];
    }
  }
  return [];
}

export function saveCustomModels(models: ThinkingModelDetail[]) {
  localStorage.setItem(CUSTOM_MODELS_STORAGE_KEY, JSON.stringify(models));
}

export function deleteCustomModel(id: string) {
  const customModels = loadCustomModels();
  const updatedModels = customModels.filter(model => model.id !== id);
  saveCustomModels(updatedModels);
}

export function updateCustomModel(updatedModel: ThinkingModelDetail) {
  const customModels = loadCustomModels();
  const index = customModels.findIndex(model => model.id === updatedModel.id);
  if (index !== -1) {
    customModels[index] = updatedModel;
    saveCustomModels(customModels);
  } else {
    console.warn(`Model with ID ${updatedModel.id} not found for update.`);
  }
}

export function getAllThinkingModelDetails(): ThinkingModelDetail[] {
  const customModels = loadCustomModels();
  return [...defaultThinkingModels, ...customModels];
}

export function getThinkingModelDetailsById(id: ThinkingModel): ThinkingModelDetail | undefined {
  const allModels = getAllThinkingModelDetails();
  return allModels.find(model => model.id === id);
}

export class LLMSystemicAnalyzer {
  private activeModel: any;

  constructor() {
    this.activeModel = getActiveModelConfig();
  }

  async generateAnalysis(topic: string, model: ThinkingModel, depth: number, context: string): Promise<ModelResult> {
    const prompt = this.buildPrompt(topic, model, depth, context);

    const startTime = performance.now();

    try {
      if (this.activeModel?.apiKey) {
        const { text, tokenCount } = await this.callLLMAPI(prompt, model);
        const parsedResult = this.parseResponse(text, model);
        const endTime = performance.now();
        const duration = endTime - startTime;
        const tokensPerSecond = tokenCount > 0 ? (tokenCount / (duration / 1000)) : 0;
        return { ...parsedResult, tokenCount, duration, tokensPerSecond };
      } else {
        // 回退到本地生成
        const result = this.generateLocalAnalysis(topic, model, depth);
        const endTime = performance.now();
        const duration = endTime - startTime;
        return { ...result, duration }; // 本地生成没有tokenCount
      }
    } catch (error) {
      console.error(`LLM分析失败，回退到本地生成:`, error);
      const result = this.generateLocalAnalysis(topic, model, depth);
      const endTime = performance.now();
      const duration = endTime - startTime;
      return { ...result, duration };
    }
  }

  async summarizeText(text: string, maxLength: number = 100): Promise<string> {
    if (!text || text.length <= maxLength) return text;

    const prompt = `请将以下文本总结为不超过 ${maxLength} 个字的精简描述，保留核心信息：\n\n${text}`;

    try {
      if (this.activeModel?.apiKey) {
        const { text: summaryText } = await this.callLLMAPI(prompt, 'summary-request'); // Use a special model ID for summarization
        return summaryText.trim();
      } else {
        return text.substring(0, maxLength) + '...';
      }
    }
    catch (error) {
      console.error('LLM摘要失败:', error);
      return text.substring(0, maxLength) + '...';
    }
  }

  private buildPrompt(topic: string, model: ThinkingModel, depth: number, context: string): string {
    const modelDetail = getThinkingModelDetailsById(model)
    const modelDesc = modelDetail ? modelDetail.description : `${model}分析方法`;

    let prompt = `\n请使用"${modelDetail?.name || model}"对主题"${topic}"进行深度系统化分析。\n`;

    if (context) {
      prompt += `\n思考背景：\n${context}\n`;
    }

    prompt += `\n分析要求：\n1. 思考深度：${depth}层（1=浅层，5=极深）\n2. 生成${3 + depth}个关键分析节点\n3. 每个节点包含具体的洞察和影响分析\n4. 建立节点间的逻辑连接关系\n\n请严格按照以下JSON格式返回：\n\n{\n  "name": "${modelDetail?.name || model}",\n  "description": "${modelDesc}",\n  "insights": [\n    "核心洞察1",\n    "核心洞察2",\n    "核心洞ोष3"\n  ],\n  "nodes": [\n    {\n      "id": "node-1",\n      "title": "节点标题",\n      "description": "节点详细描述",\n      "level": 0,\n      "impact": "critical|high|medium|low",\n      "insights": ["具体洞察1", "具体洞察2"]\n    }\n  ],\n  "connections": [\n    {\n      "from": "node-1",\n      "to": "node-2",\n      "type": "strong|weak",\n      "description": "连接关系描述",\n      "strength": 0.8\n    }\n  ],\n  "keyFindings": [\n    "关键发现1",\n    "关键发现2"\n  ],\n  "recommendations": [\n    "建议1",\n    "建议2"\n  ]\n}\n\n注意：\n- 节点level从0开始，0为核心节点\n- impact表示影响程度\n- strength为0-1的数值，表示连接强度\n- 确保所有节点ID唯一\n- 连接的from和to必须对应存在的节点ID\n`;

    return prompt;
  }

  private async callLLMAPI(prompt: string, model?: ThinkingModel): Promise<{ text: string, tokenCount: number }> {
    // 这里调用配置的LLM API
    if (this.activeModel.provider === 'gemini') {
      return await this.callGeminiAPI(prompt, model);
    } else if (this.activeModel.provider === 'deepseek') {
      return await this.callDeepSeekAPI(prompt, model);
    } else if (this.activeModel.provider === 'zhipu') {
      return await this.callZhipuAPI(prompt, model);
    } else {
      return await this.callCustomAPI(prompt, model);
    }
  }

  private async callGeminiAPI(prompt: string, model?: ThinkingModel): Promise<{ text: string, tokenCount: number }> {
    const { GoogleGenerativeAI } = await import('@google/generative-ai');
    const genAI = new GoogleGenerativeAI(this.activeModel.apiKey);
    const geminiModel = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });

    try {
      const result = await geminiModel.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      const tokenCount = (response as any).usageMetadata?.totalTokenCount || 0;
      console.log(`Gemini API Response (model: ${model}):`, text);
      return { text, tokenCount };
    } catch (error) {
      console.error(`Gemini API Error (model: ${model}):`, error);
      throw error; // Re-throw to be caught by generateAnalysis
    }
  }

  private async callDeepSeekAPI(prompt: string, model?: ThinkingModel): Promise<{ text: string, tokenCount: number }> {
    try {
      const response = await fetch(`${this.activeModel.baseUrl}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.activeModel.apiKey}`
        },
        body: JSON.stringify({
          model: 'gpt-4',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.7,
          max_tokens: 4000
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error(`DeepSeek API Request Failed (model: ${model}): ${response.status}`, errorData);
        throw new Error(`DeepSeek API 请求失败: ${response.status} - ${errorData.message || JSON.stringify(errorData)}`);
      }

      const data = await response.json();
      const text = data.choices[0]?.message?.content || '';
      const tokenCount = data.usage?.total_tokens || 0;
      console.log(`DeepSeek API Response (model: ${model}):`, text);
      return { text, tokenCount };
    } catch (error) {
      console.error(`DeepSeek API Error (model: ${model}):`, error);
      throw error;
    }
  }

  private async callZhipuAPI(prompt: string, model?: ThinkingModel): Promise<{ text: string, tokenCount: number }> {
    try {
      const response = await fetch(`${this.activeModel.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.activeModel.apiKey}`
        },
        body: JSON.stringify({
          model: 'glm-4',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.7,
          max_tokens: 4000
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error(`Zhipu API Request Failed (model: ${model}): ${response.status}`, errorData);
        throw new Error(`Zhipu API 请求失败: ${response.status} - ${errorData.message || JSON.stringify(errorData)}`);
      }

      const data = await response.json();
      const text = data.choices[0]?.message?.content || '';
      const tokenCount = data.usage?.total_tokens || 0;
      console.log(`Zhipu API Response (model: ${model}):`, text);
      return { text, tokenCount };
    } catch (error) {
      console.error(`Zhipu API Error (model: ${model}):`, error);
      throw error;
    }
  }

  private async callCustomAPI(prompt: string, model?: ThinkingModel): Promise<{ text: string, tokenCount: number }> {
    try {
      const response = await fetch(`${this.activeModel.baseUrl}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.activeModel.apiKey}`
        },
        body: JSON.stringify({
          prompt: prompt,
          max_tokens: 4000,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error(`Custom API Request Failed (model: ${model}): ${response.status}`, errorData);
        throw new Error(`自定义 API 请求失败: ${response.status} - ${errorData.message || JSON.stringify(errorData)}`);
      }

      const data = await response.json();
      const text = data.text || data.response || data.content || '';
      const tokenCount = data.usage?.total_tokens || 0; // 假设自定义API返回usage.total_tokens
      console.log(`Custom API Response (model: ${model}):`, text);
      return { text, tokenCount };
    } catch (error) {
      console.error(`Custom API Error (model: ${model}):`, error);
      throw error;
    }
  }

  private parseResponse(text: string, model: ThinkingModel): ModelResult {
    try {
      // 清理响应文本
      const cleanText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const parsed = JSON.parse(cleanText);

      // 确保节点有正确的modelSource
      parsed.nodes = parsed.nodes.map((node: any) => ({
        ...node,
        modelSource: model,
        id: `${model}-${node.id}`
      }));

      // 更新连接中的节点ID
      parsed.connections = parsed.connections.map((conn: any) => ({
        ...conn,
        from: `${model}-${conn.from}`,
        to: `${model}-${conn.to}`
      }));

      return parsed;
    } catch (error) {
      console.error('解析LLM响应失败:', error, '原始文本:', text);
      throw new Error('LLM返回的数据格式不正确');
    }
  }

  private generateLocalAnalysis(topic: string, model: ThinkingModel, depth: number): ModelResult {
    // 本地回退分析逻辑
    const templates = this.getLocalTemplates(model);
    const nodeCount = Math.min(3 + depth, 8);

    const nodes: AnalysisNode[] = [];
    const connections: NodeConnection[] = [];

    // 生成核心节点
    nodes.push({
      id: `${model}-core`,
      title: `${topic}核心`,
      description: `从${model}角度分析${topic}的核心要素`,
      level: 0,
      impact: 'critical',
      modelSource: model,
      insights: templates.coreInsights
    });

    // 生成其他层级节点
    for (let level = 1; level <= depth; level++) {
      const levelNodeCount = Math.min(2 + level, 4);
      for (let i = 0; i < levelNodeCount && nodes.length < nodeCount; i++) {
        const template = templates.levelTemplates[level - 1]?.[i] || templates.levelTemplates[0][0];
        nodes.push({
          id: `${model}-${level}-${i}`,
          title: template.title,
          description: `${topic}的${template.description}`,
          level,
          impact: level <= 2 ? 'high' : 'medium',
          modelSource: model,
          insights: template.insights
        });
      }
    }

    // 生成连接
    for (let i = 0; i < nodes.length - 1; i++) {
      for (let j = i + 1; j < nodes.length; j++) {
        if (Math.random() > 0.6) {
          connections.push({
            from: nodes[i].id,
            to: nodes[j].id,
            type: Math.random() > 0.5 ? 'strong' : 'weak',
            description: `${nodes[i].title}影响${nodes[j].title}`,
            strength: Math.random() * 0.5 + 0.5
          });
        }
      }
    }

    return {
      name: templates.name,
      description: templates.description,
      insights: templates.insights,
      nodes,
      connections,
      keyFindings: templates.keyFindings,
      recommendations: templates.recommendations
    };
  }

  private getLocalTemplates(model: ThinkingModel) {
    const templates = {
      'butterfly-effect': {
        name: '蝴蝶效应分析',
        description: '分析微小变化可能产生的巨大连锁反应',
        insights: ['微小变化的放大效应', '非线性系统响应', '连锁反应机制'],
        coreInsights: ['识别敏感触发点', '追踪影响传播路径'],
        levelTemplates: [
          [
            { title: '微小触发因素', description: '可能引发连锁反应的微小因素', insights: ['识别关键触发点', '评估初始影响'] },
            { title: '放大机制', description: '将小变化放大的系统机制', insights: ['分析放大路径', '理解非线性效应'] }
          ]
        ],
        keyFindings: ['关键敏感点识别', '影响传播路径'],
        recommendations: ['监控关键触发点', '建立早期预警机制']
      },
      'premortem': {
        name: '事前验尸分析',
        description: '预先分析可能的失败原因和风险点',
        insights: ['潜在失败模式', '风险预防策略', '系统脆弱性'],
        coreInsights: ['识别关键风险点', '评估失败概率'],
        levelTemplates: [
          [
            { title: '关键风险因素', description: '最可能导致失败的风险因素', insights: ['风险概率评估', '影响程度分析'] },
            { title: '系统脆弱点', description: '系统中最薄弱的环节', insights: ['脆弱性识别', '加固策略'] }
          ]
        ],
        keyFindings: ['主要风险源', '系统薄弱环节'],
        recommendations: ['制定风险缓解策略', '加强薄弱环节']
      }
      // 其他模型的模板...
    };

    return templates[model as keyof typeof templates] || templates['butterfly-effect'];
  }
}

// 主要的分析生成函数
export async function generateSystemicAnalysis(
  topic: string,
  models: ThinkingModel[],
  depth: number,
  context: string,
  onProgress?: (step: number) => void
): Promise<AnalysisResult> {

  const analyzer = new LLMSystemicAnalyzer()
  const result: AnalysisResult = {
    nodes: [],
    connections: [],
    modelResults: {},
    summary: {
      totalNodes: 0,
      totalConnections: 0,
      keyInsights: [],
      criticalFactors: [],
      totalTokenCount: 0,
      totalDuration: 0,
      averageTokensPerSecond: 0
    }
  }

  let currentStep = 0
  let accumulatedTokenCount = 0
  let accumulatedDuration = 0

  for (const modelId of models) {
    try {
      // 模拟分析过程
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const modelResult = await analyzer.generateAnalysis(topic, modelId, depth, context)
      
      result.modelResults[modelId] = modelResult
      result.nodes.push(...modelResult.nodes)
      result.connections.push(...modelResult.connections)
      accumulatedTokenCount += modelResult.tokenCount || 0
      accumulatedDuration += modelResult.duration || 0
      
      currentStep++
      onProgress?.(currentStep)
    } catch (error) {
      console.error(`模型 ${modelId} 分析失败:`, error)
      currentStep++
      onProgress?.(currentStep)
    }
  }

  // 去重和优化连接
  result.connections = deduplicateConnections(result.connections)
  
  // 生成总结
  result.summary = generateSummary(result)
  result.summary.totalTokenCount = accumulatedTokenCount
  result.summary.totalDuration = accumulatedDuration
  result.summary.averageTokensPerSecond = accumulatedTokenCount > 0 && accumulatedDuration > 0 
    ? (accumulatedTokenCount / (accumulatedDuration / 1000)) 
    : 0
  
  saveAnalysisResult(topic, result)
  return result
}

// 去重连接
function deduplicateConnections(connections: NodeConnection[]): NodeConnection[] {
  const seen = new Set<string>()
  return connections.filter(conn => {
    const key = `${conn.from}-${conn.to}`
    const reverseKey = `${conn.to}-${conn.from}`
    if (seen.has(key) || seen.has(reverseKey)) return false
    seen.add(key)
    return true
  })
}

// 保存分析结果到 localStorage
function saveAnalysisResult(topic: string, result: AnalysisResult) {
  localStorage.setItem(`systemic-analysis-result-${topic}`, JSON.stringify(result))
}

// 从 localStorage 加载分析结果
export function loadAnalysisResult(topic: string): AnalysisResult | null {
  const saved = localStorage.getItem(`systemic-analysis-result-${topic}`)
  if (saved) {
    try {
      return JSON.parse(saved)
    } catch (error) {
      console.error('Failed to load analysis result:', error)
      return null
    }
  }
  return null
}

// 生成分析总结
function generateSummary(result: AnalysisResult) {
  
  const keyInsights: string[] = []
  const criticalFactors: string[] = []
  
  // 收集关键洞察
  Object.values(result.modelResults).forEach(modelResult => {
    if (modelResult) {
      keyInsights.push(...modelResult.insights.slice(0, 2))
      criticalFactors.push(...modelResult.keyFindings.slice(0, 1))
    }
  })
  
  return {
    totalNodes: result.nodes.length,
    totalConnections: result.connections.length,
    keyInsights: [...new Set(keyInsights)].slice(0, 5),
    criticalFactors: [...new Set(criticalFactors)].slice(0, 3)
  }
}